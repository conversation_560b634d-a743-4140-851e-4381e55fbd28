/* pages/order/success.wxss */
.container {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
}

.success-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 30rpx;
  border-left: 6rpx solid #fff;
  border-bottom: 6rpx solid #fff;
  transform: translate(-50%, -70%) rotate(-45deg);
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.success-amount {
  font-size: 60rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.success-info {
  width: 100%;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.success-tip {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

.action-buttons {
  display: flex;
  width: 100%;
  margin-bottom: 40rpx;
}

.action-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
  border: 1rpx solid #ddd;
  color: #666;
}

.action-button.primary {
  background-color: var(--primary-color);
  color: #fff;
  border: none;
}

.home-button {
  font-size: 28rpx;
  color: var(--primary-color);
  text-decoration: underline;
}
