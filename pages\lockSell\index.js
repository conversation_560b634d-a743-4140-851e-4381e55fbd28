const app = getApp();

Page({
  data: {
    repurchasePrice: '--', // 今日回购价
    weight: '', // 卖出克重
    estimatedAmount: '0.00', // 预估金额
    serviceFee: '0.00', // 总服务费
    deposit: '0.00', // 定金
    selectedAddress: null, // 选中的收货地址
    agreementChecked: false, // 协议是否勾选
    canSell: false // 是否可以立即卖出
  },

  onLoad() {
    // 加载今日回购价
    this.loadRepurchasePrice();
  },

  // 加载今日回购价
  loadRepurchasePrice() {
    wx.showLoading({
      title: '加载金价...',
      mask: true
    });

    wx.request({
      url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 200 && res.data && res.data.code === 1 && res.data.data && res.data.data.list) {
          const goldData = res.data.data.list;
          const mainGold = goldData.Au9999 || {};
          const repurchasePrice = mainGold.sellprice || '--'; // 卖出金价对应回购价

          this.setData({
            repurchasePrice: repurchasePrice
          });
          // 加载完价格后重新计算金额
          this.calculateAmount();
        } else {
          console.error('获取回购价失败', res);
          wx.showToast({
            title: '获取金价失败',
            icon: 'none'
          });
          this.setData({
            repurchasePrice: '--'
          });
           this.calculateAmount(); // 即使失败也尝试计算一次，更新按钮状态
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求金价API失败', err);
        wx.showToast({
          title: '网络错误，获取金价失败',
          icon: 'none'
        });
        this.setData({
          repurchasePrice: '--'
        });
         this.calculateAmount(); // 即使失败也尝试计算一次，更新按钮状态
      }
    });
  },

  // 卖出克重输入
  onWeightInput(e) {
    const weight = e.detail.value;
    this.setData({
      weight: weight
    });
    this.calculateAmount();
  },

  // 计算预估金额、服务费和定金
  calculateAmount() {
    const weight = parseFloat(this.data.weight);
    const price = parseFloat(this.data.repurchasePrice);

    if (isNaN(weight) || weight <= 0 || isNaN(price) || price <= 0) { // 价格为--时也重置
      this.setData({
        estimatedAmount: '0.00',
        serviceFee: '0.00',
        deposit: '0.00',
        canSell: false
      });
      return;
    }

    // TODO: 根据实际业务逻辑计算服务费和定金
    const estimatedAmount = weight * price;
    const serviceFee = estimatedAmount * 0.01; // 假设服务费率1%
    const deposit = estimatedAmount * 0.05; // 假设定金比例5%

    this.setData({
      estimatedAmount: estimatedAmount.toFixed(2),
      serviceFee: serviceFee.toFixed(2),
      deposit: deposit.toFixed(2),
      canSell: this.checkCanSell(weight, this.data.selectedAddress, this.data.agreementChecked)
    });
  },

  // 选择收货地址
  selectAddress() {
    wx.navigateTo({
      url: '/pages/address/index?from=lockSell' // 跳转到地址列表页面，并带上来源标识
    });
  },

  // 从地址列表页面返回时接收选中的地址数据
  // 在地址列表页面调用 prevPage.setData({ selectedAddress: selectedAddress }) 后，本页面的 onShow 会被触发
  // 并在 onShow 中可以获取到传递过来的 selectedAddress
  onShow() {
    // 检查是否有从地址列表页面传递过来的 selectedAddress
    const selectedAddress = this.data.selectedAddress; // onShow时data已经更新
    if (selectedAddress) {
       // TODO: 确认地址数据格式是否与后端list接口返回一致
       // 如果不一致，需要在这里进行映射或调整loadAddressList的映射逻辑
       this.setData({
         selectedAddress: selectedAddress, // 确保data中的selectedAddress是最新的
         canSell: this.checkCanSell(parseFloat(this.data.weight), selectedAddress, this.data.agreementChecked)
       });
       // 清除传递过来的地址数据，避免重复设置
       // 如果地址选择页面的返回逻辑会将数据设置到 prevPage 的话，这里不需要手动清除
       // 如果是通过全局变量或 event channel 传递，则需要在此处获取并清除
    } else {
      // 如果没有传递地址回来，可能是第一次进入或取消选择
      this.setData({
        canSell: this.checkCanSell(parseFloat(this.data.weight), null, this.data.agreementChecked)
      });
    }
    // 重新计算金额以更新按钮状态
    this.calculateAmount();
  },

  // 协议勾选变化
  onAgreementChange(e) {
    const checked = e.detail.value.length > 0;
    this.setData({
      agreementChecked: checked,
      canSell: this.checkCanSell(parseFloat(this.data.weight), this.data.selectedAddress, checked)
    });
  },

  // 检查是否可以立即卖出
  checkCanSell(weight, address, agreementChecked) {
    return weight > 0 && 
           address !== null && 
           agreementChecked;
  },

  // 提交卖出订单
  submitSell() {
    if (!this.data.canSell) return;

    // TODO: 调用API提交卖出订单
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 准备API请求数据
    const apiData = {
      // 根据后端API文档构建数据
      weight: parseFloat(this.data.weight),
      addressId: this.data.selectedAddress.addressId,
      // 其他可能需要的字段，如用户ID，定金金额等
    };

    // 模拟API请求
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '提交成功',
        content: '卖出订单已提交',
        showCancel: false,
        success: () => {
          // 提交成功后跳转到订单详情或其他页面
          // wx.redirectTo({ url: '/pages/orderDetail/index' });
        }
      });
    }, 2000);
  }
}) 