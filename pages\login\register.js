// pages/login/register.js
const app = getApp();
const userApi = require('../../utils/userapi');

Page({
  data: {
    account: '',
    password: '',
    confirmPassword: '',
    username: '',
    roleLevel: '1',
    userLevel: '0'
  },

  // 输入账号
  inputAccount: function(e) {
    this.setData({
      account: e.detail.value
    });
  },

  // 输入密码
  inputPassword: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 输入确认密码
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 输入用户名
  inputUsername: function(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 注册
  register: function() {
    const { account, password, confirmPassword, username, roleLevel } = this.data;

    // 表单验证
    if (!account) {
      wx.showToast({
        title: '请输入账号',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    if (!username) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '注册中...',
    });

    // 准备注册数据
    const userData = {
      account,
      password,
      roleLevel,
      username
    };

    // 使用API模块发送注册请求
    userApi.register(userData)
      .then(res => {
        wx.hideLoading();

        if (res.code === 200) {
          // 注册成功
          wx.showToast({
            title: '注册成功请登录',
            icon: 'success'
          });

          // 延迟跳转到登录页面
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
        } else {
          // 注册失败
          wx.showToast({
            title: res.msg || '注册失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.msg || '网络错误，请重试',
          icon: 'none'
        });
        console.error('注册请求失败', err);
      });
  },

  // 显示用户协议
  showUserAgreement: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }

})
