/* pages/commission/index.wxss */
.container {
  padding-bottom: 30rpx;
}

/* 佣金卡片 */
.commission-card {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  color: #fff;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.3);
}

.commission-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.commission-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.commission-tip {
  font-size: 26rpx;
  opacity: 0.8;
  margin-bottom: 30rpx;
}

.withdraw-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 30rpx;
  padding: 16rpx 0;
  font-size: 30rpx;
  font-weight: bold;
  width: 60%;
  margin: 0 auto;
}

/* 标签栏 */
.tab-bar {
  display: flex;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

/* 佣金明细 */
.commission-list {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.commission-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.commission-item:last-child {
  border-bottom: none;
}

.commission-info {
  flex: 1;
}

.commission-source {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.commission-level {
  font-size: 22rpx;
  color: #fff;
  background-color: #FF9800;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
}

.commission-time {
  font-size: 24rpx;
  color: #999;
}

.commission-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B00;
  text-align: right;
}

.commission-value.settled {
  color: #07C160;
}

.commission-status {
  display: block;
  font-size: 22rpx;
  font-weight: normal;
  color: #FF6B00;
}

.commission-status.settled {
  color: #07C160;
}

/* 提现记录 */
.withdraw-list {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.withdraw-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.withdraw-item:last-child {
  border-bottom: none;
}

.withdraw-info {
  flex: 1;
}

.withdraw-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.withdraw-time {
  font-size: 24rpx;
  color: #999;
}

.withdraw-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: right;
}

.withdraw-status {
  display: block;
  font-size: 22rpx;
  font-weight: normal;
}

.withdraw-status.pending {
  color: #FF9800;
}

.withdraw-status.approved {
  color: #07C160;
}

.withdraw-status.rejected {
  color: #FF5252;
}

/* 加载状态 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.empty {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 提现面板 */
.withdraw-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
}

.withdraw-modal.show {
  visibility: visible;
}

.withdraw-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.withdraw-modal.show .withdraw-mask {
  opacity: 1;
}

.withdraw-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.withdraw-modal.show .withdraw-content {
  transform: translateY(0);
}

.withdraw-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.withdraw-available {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.withdraw-input-container {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.withdraw-currency {
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.withdraw-input {
  flex: 1;
  font-size: 40rpx;
  font-weight: bold;
}

.withdraw-all {
  text-align: right;
  font-size: 26rpx;
  color: var(--primary-color);
  margin-bottom: 30rpx;
}

.withdraw-submit {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.withdraw-tips {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}

.withdraw-tip-item {
  margin-bottom: 6rpx;
}
