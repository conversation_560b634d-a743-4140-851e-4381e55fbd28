/* pages/address/index.wxss */
page {
  --primary-color: #4A1010;
  --secondary-color: #6B1919;
  --primary-gradient: linear-gradient(135deg, #6B1919, #4A1010);
  --text-color: #333333;
  --light-text-color: #666666;
  --background-color: #F8F0F0;
  --border-color: #D9C1C1;
  background-color: var(--background-color);
}

.container {
  padding-bottom: 120rpx;
}

/* 地址列表 */
.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.address-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.address-name-phone {
  display: flex;
  align-items: center;
}

.address-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
}

.address-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(to right, #FF4D4F, #D4380D);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #f9f9f9;
}

.action-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加地址按钮 */
.add-address-btn {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  height: 88rpx;
  background: var(--primary-gradient);
  color: #fff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(74, 16, 16, 0.3);
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
}

.btn-text {
  font-size: 30rpx;
}

/* 地址表单弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.address-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.address-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-label.required::before {
  content: '*';
  color: #FF4D4F;
  margin-right: 5rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #eee;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #eee;
}

.region-picker {
  width: 100%;
  height: 88rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #eee;
  display: flex;
  align-items: center;
}

.region-picker.placeholder {
  color: #999;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.submit-btn {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(to right, #FF4D4F, #D4380D);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 30rpx;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}
