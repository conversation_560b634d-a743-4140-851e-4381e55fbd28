<!--pages/goldRecycle/index.wxml-->
<view class="container">
  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item {{step >= 1 ? 'active' : ''}}">
      <view class="step-number-1">1</view>
      <view class="step-text">选择类型</view>
    </view>
    <view class="step-line {{step >= 2 ? 'active' : ''}}"></view>
    <view class="step-item {{step >= 2 ? 'active' : ''}}">
      <view class="step-number-1">2</view>
      <view class="step-text">填写信息</view>
    </view>
    <view class="step-line {{step >= 3 ? 'active' : ''}}"></view>
    <view class="step-item {{step >= 3 ? 'active' : ''}}">
      <view class="step-number-1">3</view>
      <view class="step-text">提交成功</view>
    </view>
  </view>

  <!-- 步骤1：选择黄金类型 -->
  <view class="step-content" wx:if="{{step === 1}}">
    <!-- 当前金价 -->
    <view class="gold-price-card">
      <view class="gold-price-title">今日回收预估价</view>
      <view class="gold-price-value">¥{{goldPrice.buyPrice/100}} <text class="gold-price-unit">元/克</text></view>
      <view class="gold-price-update-time">更新时间: {{goldPrice.updateTime}}</view>
      <view class="gold-price-source">数据来源: 上海黄金交易所</view>
    </view>

    <!-- 黄金回收流程图 -->
    <view class="process-section">
      <view class="process-title">
        <text>回收流程</text>
      </view>

      <view class="process-flow">
        <!-- 第一行流程 -->
        <view class="process-row">
          <view class="process-step">
            <view class="step-icon">📱</view>
            <view class="step-number">1</view>
            <view class="step-title">线上下单</view>
            <view class="step-desc">称重拍照</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">🔍</view>
            <view class="step-number">2</view>
            <view class="step-title">平台审核</view>
            <view class="step-desc">信息确认</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">🚚</view>
            <view class="step-number">3</view>
            <view class="step-title">上门取件</view>
            <view class="step-desc">或到店验金</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">🏢</view>
            <view class="step-number">4</view>
            <view class="step-title">平台入库</view>
            <view class="step-desc">专业检测</view>
          </view>
        </view>

        <!-- 第二行流程 -->
        <view class="process-row">
          <view class="process-step">
            <view class="step-icon">📊</view>
            <view class="step-number">5</view>
            <view class="step-title">上传结果</view>
            <view class="step-desc">确认成色</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">✅</view>
            <view class="step-number">6</view>
            <view class="step-title">确认卖出</view>
            <view class="step-desc">价格确定</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">💰</view>
            <view class="step-number">7</view>
            <view class="step-title">平台打款</view>
            <view class="step-desc">资金到账</view>
          </view>

          <view class="process-arrow">→</view>

          <view class="process-step">
            <view class="step-icon">🎯</view>
            <view class="step-number">8</view>
            <view class="step-title">订单完结</view>
            <view class="step-desc">交易完成</view>
          </view>
        </view>
      </view>

      <!-- 重要提示 -->
      <view class="process-notice">
        <view class="notice-item">
          <view class="notice-icon">⚠️</view>
          <view class="notice-text">由于旧料可能有藏污纳垢，重量以我们收到后过火复称为准</view>
        </view>
      </view>
    </view>

    <!-- 黄金类型选择 -->
    <view class="section-title">选择黄金类型</view>
    <view class="gold-types">
      <view
        class="gold-type-item {{selectedType === item.id ? 'selected' : ''}}"
        wx:for="{{goldTypes}}"
        wx:key="id"
        bindtap="selectGoldType"
        data-type="{{item.id}}"
      >
        <image class="gold-type-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <view class="gold-type-name">{{item.name}}</view>
      </view>
    </view>

    <!-- 按钮组 -->
    <view class="button-group">
      <view class="btn-next" bindtap="nextStep">下一步</view>
    </view>
  </view>

  <!-- 步骤2：填写回收信息 -->
  <view class="step-content" wx:if="{{step === 2}}">
    <!-- 回收信息表单 -->
    <view class="form-section">
      <view class="form-item">
        <view class="form-label required">预估重量(克)</view>
        <input
          class="form-input"
          type="digit"
          placeholder="请输入预估重量"
          value="{{estimatedWeight}}"
          bindinput="inputWeight"
        />
      </view>

      <!-- 黄金类型显示预估金额和手续费 -->
      <view class="form-item" wx:if="{{estimatedWeight && goldPrice.buyPrice && selectedType === 'jewelry'}}">
        <view class="form-label">预估金额</view>
        <view class="estimated-amount">约 ¥{{estimatedAmount}} 元</view>
        <view class="fee-info" wx:if="{{userLevel}}">
          <view class="fee-item">
            <text class="fee-label">用户等级：</text>
            <text class="fee-value">{{userLevelName}}</text>
          </view>
          <view class="fee-item">
            <text class="fee-label">手续费：</text>
            <text class="fee-value">¥{{feeRate}} 元/克</text>
          </view>
          <view class="fee-item">
            <text class="fee-label">总手续费：</text>
            <text class="fee-value">¥{{totalFee}} 元</text>
          </view>
          <view class="fee-item final-amount">
            <text class="fee-label">最终金额：</text>
            <text class="fee-value">¥{{finalAmount}} 元</text>
          </view>
        </view>
      </view>

      <!-- 非黄金类型只显示预估手续费 -->
      <view class="form-item" wx:if="{{estimatedWeight && goldPrice.buyPrice && (selectedType === 'bar' || selectedType === 'broken' || selectedType === 'other')}}">
        <view class="form-label">预估手续费</view>
        <view class="fee-info" wx:if="{{userLevel}}">
          <view class="fee-item">
            <text class="fee-label">用户等级：</text>
            <text class="fee-value">{{userLevelName}}</text>
          </view>
          <view class="fee-item">
            <text class="fee-label">服务费率：</text>
            <text class="fee-value">{{feeRate}} 元/克</text>
          </view>
          <view class="fee-item">
            <text class="fee-label">预估手续费：</text>
            <text class="fee-value">¥{{serviceFee}} 元</text>
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="form-item" wx:if="{{estimatedWeight && goldPrice.buyPrice}}">
        <view class="amount-tip">1️.预估金额</view>
        <view class="amount-tip">*️ 我们收到货品后与您确认成色后确认最终价，最终金额=预估金额-手续费-服务费</view>
        <view class="amount-tip">2️.预估重量</view>
        <view class="amount-tip">*️ 旧品可能会有藏污纳垢对预估重量会有影响，以我们收到货品，过火后实际称重为准</view>
        <view class="amount-tip">3️.成色</view>
        <view class="amount-tip">*️ 个别旧品可能会有焊点导致成色出现不符情况，所以成色为我们收到货品过火后检测为准</view>
      </view>

      <view class="form-item">
        <view class="form-label required">成色</view>
        <view class="purity-selector" bindtap="showPuritySelector">
          <view class="purity-display {{purity ? '' : 'placeholder'}}">
            {{purity || '请选择成色'}}
          </view>
          <view class="purity-arrow">▼</view>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">备注</view>
        <textarea
          class="form-textarea"
          placeholder="请备注金子的成色、来源等信息（选填）"
          value="{{description}}"
          bindinput="inputDescription"
        ></textarea>
      </view>

      <!-- 地址选择 -->
      <view class="address-section">
        <view class="form-label required">收货地址</view>
        <view class="address-selector" bindtap="selectAddress">
          <block wx:if="{{selectedAddress}}">
            <view class="address-info">
              <text class="receiver-name">{{selectedAddress.receiverName}}</text>
              <text class="receiver-phone">{{selectedAddress.receiverPhone}}</text>
            </view>
            <view class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detailAddress}}</view>
          </block>
          <block wx:else>
            <text class="placeholder">请选择收货地址</text>
          </block>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label required">上传图片</view>
        <view class="image-uploader">
          <view class="image-item" wx:for="{{images}}" wx:key="*this">
            <image class="uploaded-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-src="{{item}}"></image>
            <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}">×</view>
          </view>

          <!-- 上传按钮 -->
          <view class="upload-button {{uploading ? 'uploading' : ''}}" bindtap="chooseImage" wx:if="{{images.length < 4 && !uploading}}">
            <view class="upload-icon">+</view>
            <view class="upload-text">添加图片</view>
          </view>

          <!-- 上传中状态 -->
          <view class="upload-button uploading" wx:if="{{uploading}}">
            <view class="upload-loading">
              <view class="loading-spinner"></view>
            </view>
            <view class="upload-text">上传中...</view>
          </view>
        </view>
        <view class="upload-tip">请上传清晰的黄金照片，最多4张</view>
        <view class="example-image-container">
          <view class="example-image-title">示例图片（点击查看大图）：</view>
          <view class="example-image-wrapper" bindtap="previewExampleImage" data-src="/images/gold-example.png">
            <image class="example-image" src="/images/gold-example.png" mode="aspectFill"></image>
            <view class="example-image-hint">查看大图</view>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label required">联系人</view>
        <input
          class="form-input"
          placeholder="请输入联系人姓名"
          value="{{contactName}}"
          bindinput="inputContactName"
        />
      </view>

      <view class="form-item">
        <view class="form-label required">联系电话</view>
        <input
          class="form-input"
          type="number"
          placeholder="请输入联系电话"
          value="{{contactPhone}}"
          bindinput="inputContactPhone"
        />
      </view>
    </view>

    <!-- 按钮组 -->
    <view class="button-group">
      <view class="btn-prev" bindtap="prevStep">
        <text class="btn-icon">←</text> 上一步
      </view>
      <view class="btn-submit {{canSubmit ? '' : 'disabled'}} {{submitLoading ? 'loading' : ''}}" bindtap="submitRecycle">
        {{submitLoading ? '提交中...' : '提交申请'}} <text class="btn-icon" wx:if="{{!submitLoading}}">→</text>
      </view>
    </view>
  </view>

  <!-- 步骤3：提交成功 -->
  <view class="step-content success-content" wx:if="{{step === 3}}">
    <view class="success-icon"></view>
    <view class="success-title">提交成功</view>
    <view class="success-desc">您的黄金回收申请已提交，我们将安排顺丰上门取件或者直接到门店验金</view>
    <view class="success-order-id">订单号: {{recycleId}}</view>

    <view class="success-buttons">
      <view class="btn-go-home" bindtap="goToHome"><text class="btn-icon">←</text> 返回首页</view>
    </view>
  </view>

  <!-- 成色选择器弹窗 -->
  <view class="purity-picker-modal {{showPurityPicker ? 'show' : ''}}" wx:if="{{currentPurityOptions.length > 0}}">
    <view class="purity-picker-mask" bindtap="hidePuritySelector"></view>
    <view class="purity-picker-content">
      <view class="purity-picker-header">
        <view class="purity-picker-title">选择成色</view>
        <view class="purity-picker-close" bindtap="hidePuritySelector">×</view>
      </view>
      <view class="purity-picker-list">
        <view
          class="purity-picker-item {{selectedPurityIndex === index ? 'selected' : ''}}"
          wx:for="{{currentPurityOptions}}"
          wx:key="value"
          bindtap="selectPurity"
          data-index="{{index}}"
        >
          <view class="purity-picker-label">{{item.label}}</view>
          <view class="purity-picker-check" wx:if="{{selectedPurityIndex === index}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 悬浮客服按钮 -->
  <view class="floating-service-btn" bindtap="contactService">
    <view class="service-icon">?</view>
  </view>
</view>
