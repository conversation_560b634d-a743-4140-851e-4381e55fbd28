// pages/order/list.js
const app = getApp();

Page({
  data: {
    tabs: [
      { id: 'all', name: '全部' },
      { id: 1, name: '待处理' },
      { id: 2, name: '处理中' },
      { id: 3, name: '已完成' },
      { id: 4, name: '已取消' }
    ],
    currentTab: 'all',
    orders: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    total: 0
  },

  onLoad: function (options) {
    // 如果有状态参数，设置当前标签
    if (options.status) {
      this.setData({
        currentTab: options.status
      });
    }

    this.loadOrders(true);
  },

  onShow: function () {
    // 页面显示时检查是否需要刷新数据
    if (this.data.needRefresh) {
      this.loadOrders(true);
      this.setData({
        needRefresh: false
      });
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadOrders(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders(false);
    }
  },

  // 加载订单列表
  loadOrders: function (refresh) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        orders: []
      });
    }

    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }

    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.isLogin || !userInfo.account) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return new Promise((resolve) => {
      // 构建API请求参数
      let apiUrl = `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/account/${userInfo.account}`;
      let requestData = {
        pageNum: this.data.page,
        pageSize: this.data.pageSize
      };

      // 状态筛选
      if (this.data.currentTab !== 'all') {
        requestData.status = this.data.currentTab;
      }

      wx.request({
        url: apiUrl,
        method: 'GET',
        data: requestData,
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('订单列表API响应:', res);
          this.setData({ loading: false });

          if (res.data.code === 200 && res.data.data) {
            const responseData = res.data.data;
            const records = responseData.records || [];

            // 处理订单数据
            const processedOrders = records.map(order => this.processOrderItem(order));

            // 根据当前页数决定是追加还是替换数据
            const allOrders = this.data.page === 1
              ? processedOrders
              : [...this.data.orders, ...processedOrders];

            this.setData({
              orders: allOrders,
              total: responseData.total || 0,
              hasMore: responseData.current < responseData.pages,
              page: this.data.page + 1
            });
          } else {
            wx.showToast({
              title: res.data.message || '获取订单列表失败',
              icon: 'none'
            });
          }
          resolve();
        },
        fail: (error) => {
          console.error('获取订单列表失败:', error);
          this.setData({ loading: false });
          wx.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
          resolve();
        }
      });
    });
  },

  // 处理单条订单数据
  processOrderItem: function (order) {
    // 处理图片列表
    let imageList = [];

    // 优先使用 imageList 字段
    if (order.imageList && Array.isArray(order.imageList)) {
      imageList = order.imageList;
    }
    // 如果 imageList 不存在，尝试解析 imageBase64 字段
    else if (order.imageBase64) {
      try {
        // imageBase64 可能是字符串化的JSON数组
        if (typeof order.imageBase64 === 'string') {
          // 尝试解析JSON字符串
          const parsed = JSON.parse(order.imageBase64);
          if (Array.isArray(parsed)) {
            imageList = parsed;
          } else {
            // 如果不是数组，可能是单个URL字符串
            imageList = [order.imageBase64];
          }
        } else if (Array.isArray(order.imageBase64)) {
          imageList = order.imageBase64;
        }
      } catch (error) {
        console.error('解析图片数据失败:', error, order.imageBase64);
        // 解析失败时，尝试直接使用原始数据
        if (order.imageBase64) {
          imageList = [order.imageBase64];
        }
      }
    }

    // 过滤掉无效的图片URL
    imageList = imageList.filter(url => url && typeof url === 'string' && url.trim() !== '');

    console.log('处理订单图片:', {
      orderId: order.orderId,
      originalImageBase64: order.imageBase64,
      originalImageList: order.imageList,
      processedImageList: imageList
    });

    return {
      id: order.id,
      orderId: order.orderId,
      account: order.account,
      goldType: order.goldType,
      goldTypeText: this.getGoldTypeText(order.goldType),
      goldCondition: order.goldCondition,
      purity: order.purity,
      estimatedWeight: this.formatWeight(order.estimatedWeight),
      estimatedPrice: this.formatPrice(order.estimatedPrice),
      finalPrice: this.formatPrice(order.finalPrice),
      status: order.status,
      statusText: this.getStatusText(order.status),
      description: order.description,
      inspectionResult: order.inspectionResult,
      receiverName: order.receiverName,
      receiverPhone: order.receiverPhone,
      receiverAddress: order.receiverAddress,
      imageBase64: order.imageBase64,
      imageList: imageList, // 处理后的图片列表
      hasImage: imageList.length > 0, // 是否有图片
      firstImage: imageList.length > 0 ? imageList[0] : '', // 第一张图片URL
      createTime: order.createTime,
      updateTime: order.updateTime,
      createTimeFormatted: this.formatTime(order.createTime),
      updateTimeFormatted: this.formatTime(order.updateTime),
      rawData: order // 保留原始数据以备后用
    };
  },

  // 获取状态文字
  getStatusText: function (status) {
    const statusMap = {
      0: '已取消',
      1: '已下单',
      2: '待取件',
      3: '待检测',
      4: '已检测',
      5: '已确认',
      6: '订单已完成'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取黄金类型文字
  getGoldTypeText: function (goldType) {
    const typeMap = {
      'jewelry': '黄金',
      'broken': '钯金',
      'bar': '铂金',
      'other': '其他'
    };
    return typeMap[goldType] || goldType || '未知类型';
  },

  // 格式化价格
  formatPrice: function (price) {
    if (price === null || price === undefined) return '0.00';
    return Number(price).toFixed(2);
  },

  // 格式化重量
  formatWeight: function (weight) {
    if (weight === null || weight === undefined) return '0.00';
    return Number(weight).toFixed(2);
  },

  // 切换标签
  changeTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab
      });
      this.loadOrders(true);
    }
  },

  // 跳转到订单详情
  goToOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderid;
    wx.navigateTo({
      url: '/pages/order/detail?orderId=' + orderId
    });
  },

  // 取消订单
  cancelOrder: function (e) {
    console.log(e.currentTarget.dataset)
    const orderId = e.currentTarget.dataset.id;
    const orderIndex = e.currentTarget.dataset.index;

    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          // 调用取消订单API
          wx.request({
            url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${orderId}/cancel`,
            method: 'PUT',
            header: {
              'Authorization': `Bearer ${wx.getStorageSync('token')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            success: (res) => {
              wx.hideLoading();
              if (res.data.code === 200) {
                // 更新本地数据
                const orders = this.data.orders;
                orders[orderIndex].status = 0; // 0 = 已取消
                orders[orderIndex].statusText = '已取消';
                this.setData({
                  orders: orders
                });

                wx.showToast({
                  title: '订单已取消',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.data.message || '取消失败',
                  icon: 'none'
                });
              }
            },
            fail: (error) => {
              wx.hideLoading();
              console.error('取消订单失败:', error);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });

    // 阻止事件冒泡
    return false;
  },

  // 联系客服
  contactService: function (e) {
    // 获取订单号
    let orderId = '';
    if (e && e.currentTarget && e.currentTarget.dataset) {
      orderId = e.currentTarget.dataset.orderid;
    }

    // 如果没有从事件中获取到订单号，尝试从当前点击的订单卡片获取
    if (!orderId && e && e.target) {
      // 向上查找包含订单号的父元素
      let target = e.target;
      while (target && !orderId) {
        if (target.dataset && target.dataset.orderid) {
          orderId = target.dataset.orderid;
          break;
        }
        target = target.parentNode;
      }
    }

    // 跳转到客服页面
    const url = orderId
      ? `/pages/customerService/index?orderId=${orderId}`
      : '/pages/customerService/index';

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('跳转客服页面失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });

    // 阻止事件冒泡
    return false;
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 图片加载成功
  onImageLoad: function(e) {
    const orderId = e.currentTarget.dataset.orderid;
    console.log('图片加载成功:', orderId);
  },

  // 图片加载失败
  onImageError: function(e) {
    const orderId = e.currentTarget.dataset.orderid;
    console.error('图片加载失败:', orderId, e.detail);

    // 可以在这里设置默认图片或者隐藏图片
    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 1000
    });
  }
})
