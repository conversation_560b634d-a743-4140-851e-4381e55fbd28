const app = getApp();

Page({
  data: {
    accountInfo: {
      balance: '0.00'
    },
    amount: '',
    remark: '', // 备注信息
    imageUrl: '', // 转账截图
    quickAmounts: [100, 500, 1000, 2000],
    canSubmit: false,
    submitting: false // 提交状态
  },

  onLoad() {
    this.getAccountInfo();
  },

  // 获取账户信息
  getAccountInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) return;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${userInfo.account}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            accountInfo: {
              balance: res.data.data.balance.toFixed(2)
            }
          });
        }
      }
    });
  },



  // 输入金额
  onAmountInput(e) {
    const amount = e.detail.value;
    this.setData({
      amount
    });
    this.checkCanSubmit();
  },

  // 输入备注
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          imageUrl: tempFilePath
        });
        this.checkCanSubmit();
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage() {
    wx.previewImage({
      urls: [this.data.imageUrl],
      current: this.data.imageUrl
    });
  },

  // 删除图片
  deleteImage() {
    this.setData({
      imageUrl: ''
    });
    this.checkCanSubmit();
  },

  // 选择快捷金额
  selectQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount;
    this.setData({
      amount: amount.toString()
    });
    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { amount, imageUrl, accountInfo } = this.data;
    const numAmount = parseFloat(amount);
    const canSubmit = numAmount >= 100 &&
                     numAmount <= parseFloat(accountInfo.balance) &&
                     imageUrl && imageUrl.trim() !== '';

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交提现申请
  submitWithdraw() {
    if (!this.data.canSubmit || this.data.submitting) return;

    // 表单验证
    if (!this.data.amount) {
      wx.showToast({
        title: '请输入提现金额',
        icon: 'none'
      });
      return;
    }

    const amount = parseFloat(this.data.amount);
    if (amount < 100) {
      wx.showToast({
        title: '提现金额不能少于100元',
        icon: 'none'
      });
      return;
    }

    if (amount > parseFloat(this.data.accountInfo.balance)) {
      wx.showToast({
        title: '提现金额不能超过可用余额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请上传当前账户余额截图',
        icon: 'none'
      });
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      wx.showToast({
        title: '用户信息异常，请重新登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    // 先上传图片到minio
    this.uploadImageToMinio(this.data.imageUrl)
      .then((imageUrl) => {
        wx.showLoading({
          title: '提交申请中...',
          mask: true
        });

        // 图片上传成功后，提交提现申请
        return this.submitWithdrawWithImageUrl(imageUrl, userInfo.account, amount);
      })
      .then(() => {
        wx.hideLoading();
        this.setData({
          submitting: false
        });

        wx.showModal({
          title: '提交成功',
          content: '提现申请已提交，等待审核',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('提现申请失败:', error);

        this.setData({
          submitting: false
        });

        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        });
      });
  },

  // 上传图片到minio
  uploadImageToMinio(filePath) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      wx.uploadFile({
        url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          console.log('图片上传API响应:', res);

          try {
            const responseData = JSON.parse(res.data);
            console.log('解析后的响应数据:', responseData);

            if (responseData.code === 200) {
              const imageUrl = responseData.data;
              console.log('图片上传成功，URL:', 'https://www.iejhsgold.cn'+imageUrl);
              resolve('https://www.iejhsgold.cn'+imageUrl);
            } else {
              console.error('图片上传失败，API返回:', responseData);
              reject(new Error(responseData.message || '图片上传失败'));
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            reject(new Error('解析响应失败'));
          }
        },
        fail: (error) => {
          console.error('图片上传请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 使用图片URL提交提现申请
  submitWithdrawWithImageUrl(transferImageUrl, accountId, amount) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/account-review/submit`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        data: {
          transactionType: 2, // 2=提现
          accountId: accountId,
          amount: amount.toString(),
          remark: this.data.remark || '',
          paymentType: 2, // 固定设置为2
          transferImage: transferImageUrl
        },
        success: (res) => {
          console.log('提现申请API响应:', res);

          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.message || '提交失败'));
          }
        },
        fail: (error) => {
          console.error('提现申请请求失败:', error);
          reject(new Error('网络错误，请重试'));
        }
      });
    });
  }
}) 