const app = getApp();

Page({
  data: {
    realName: '',
    idCard: '',
    idCardFront: '',
    idCardBack: '',
    idCardFrontUrl: '', // 存储上传后的图片URL
    idCardBackUrl: '', // 存储上传后的图片URL
    submitting: false,
    uploading: false,
    agreedToAgreement: false // 是否同意协议
  },

  onLoad: function (options) {
    // 检查是否已认证
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.userLevel > 0) {
      wx.showModal({
        title: '提示',
        content: '您已完成实名认证',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 输入姓名
  inputRealName: function(e) {
    this.setData({
      realName: e.detail.value
    });
  },

  // 输入身份证号
  inputIdCard: function(e) {
    this.setData({
      idCard: e.detail.value
    });
  },

  // 选择图片
  chooseImage: function(e) {
    const type = e.currentTarget.dataset.type;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 立即上传图片
        this.uploadImage(tempFilePath, type);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传图片
  uploadImage: function(filePath, type) {
    this.setData({
      uploading: true
    });

    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    this.uploadSingleImage(filePath)
      .then((imageUrl) => {
        wx.hideLoading();

        if (type === 'front') {
          this.setData({
            idCardFront: filePath,
            idCardFrontUrl: imageUrl,
            uploading: false
          });
        } else {
          this.setData({
            idCardBack: filePath,
            idCardBackUrl: imageUrl,
            uploading: false
          });
        }

        wx.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('图片上传失败:', error);

        this.setData({
          uploading: false
        });

        wx.showToast({
          title: '图片上传失败，请重试',
          icon: 'none'
        });
      });
  },

  // 上传单个图片到minio
  uploadSingleImage: function (filePath) {
    return new Promise((resolve, reject) => {
      // 检查用户登录状态
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      wx.uploadFile({
        url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
        filePath: filePath,
        name: 'file', // 服务器接收文件的字段名
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          console.log('图片上传API响应:', res);

          try {
            // 解析响应数据
            const responseData = JSON.parse(res.data);
            console.log('解析后的响应数据:', responseData);

            // 判断API响应：code为200表示成功
            if (responseData.code === 200) {
              // 上传成功，获取图片URL
              const imageUrl = responseData.data;
              console.log('图片上传成功，URL:', 'https://www.iejhsgold.cn'+imageUrl);
              resolve('https://www.iejhsgold.cn'+imageUrl);
            } else {
              console.error('图片上传失败，API返回:', responseData);
              reject(new Error(responseData.message || '上传失败'));
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            reject(new Error('解析响应失败'));
          }
        },
        fail: (error) => {
          console.error('图片上传请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 切换协议同意状态
  toggleAgreement: function() {
    this.setData({
      agreedToAgreement: !this.data.agreedToAgreement
    });
  },

  // 查看协议
  viewAgreement: function() {
    wx.showLoading({
      title: '获取协议中...',
      mask: true
    });

    // 调用API获取协议下载链接
    this.getAgreementUrl()
      .then((agreementUrl) => {
        wx.hideLoading();

        // 下载并预览协议
        wx.downloadFile({
          url: agreementUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              wx.openDocument({
                filePath: res.tempFilePath,
                success: () => {
                  console.log('协议打开成功');
                },
                fail: (err) => {
                  console.error('协议打开失败:', err);
                  wx.showToast({
                    title: '协议打开失败',
                    icon: 'none'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '协议下载失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('协议下载失败:', err);
            wx.showToast({
              title: '协议下载失败',
              icon: 'none'
            });
          }
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('获取协议链接失败:', error);
        wx.showToast({
          title: '获取协议失败',
          icon: 'none'
        });
      });
  },

  // 获取协议下载链接
  getAgreementUrl: function() {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token');

      wx.request({
        url: 'https://www.iejhsgold.cn/mall/31d50bafa1824ef3af4a179db5a74dd5.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250603%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250603T082647Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=8ade78cbb9661a973a912c96eaf3a2c8f3e12f032636b4837bc9debcb7ab171f',
        method: 'GET',
        header: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('获取协议链接API响应:', res);

          if (res.statusCode === 200 && res.data.code === 200) {
            // 假设API返回格式为 { code: 200, data: { downloadUrl: "..." } }
            const downloadUrl = res.data.data.downloadUrl || res.data.data.url;
            if (downloadUrl) {
              resolve(downloadUrl);
            } else {
              reject(new Error('协议链接为空'));
            }
          } else {
            reject(new Error(res.data.message || '获取协议链接失败'));
          }
        },
        fail: (error) => {
          console.error('获取协议链接请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 验证表单
  validateForm: function() {
    if (!this.data.realName) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.idCard) {
      wx.showToast({
        title: '请输入身份证号',
        icon: 'none'
      });
      return false;
    }

    // 验证身份证号格式
    const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardReg.test(this.data.idCard)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.idCardFrontUrl) {
      wx.showToast({
        title: '请上传身份证正面照片',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.idCardBackUrl) {
      wx.showToast({
        title: '请上传身份证反面照片',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.agreedToAgreement) {
      wx.showToast({
        title: '请先同意贸易合作协议书',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交认证
  submitVerification: function() {
    if (!this.validateForm()) {
      return;
    }

    // 检查是否正在上传图片
    if (this.data.uploading) {
      wx.showToast({
        title: '图片正在上传中，请稍候',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    wx.showLoading({
      title: '身份证核验中...',
      mask: true
    });

    // 先进行身份证核验
    this.verifyIdCard()
      .then((verifyResult) => {
        if (verifyResult.res === "1") {
          // 核验成功，更新用户等级
          return this.updateUserLevel();
        } else {
          // 核验失败
          throw new Error('身份证核验失败：' + verifyResult.description);
        }
      })
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '实名认证成功',
          icon: 'success'
        });

        // 更新本地用户信息
        const userInfo = wx.getStorageSync('userInfo');
        userInfo.userLevel = 1;
        userInfo.realName = this.data.realName;
        userInfo.idCard = this.data.idCard;
        wx.setStorageSync('userInfo', userInfo);
        app.globalData.userInfo = userInfo;

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('认证失败', err);
        wx.showToast({
          title: err.message || '认证失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({
          submitting: false
        });
      });
  },

  // 身份证核验
  verifyIdCard: function() {
    return new Promise((resolve, reject) => {
      const apiUrl = 'https://api.tanshuapi.com/api/check_idcard/v1/index';
      const params = {
        key: 'c844cf198dc2decd1e46c05abd2d04b6',
        name: this.data.realName,
        idcard: this.data.idCard
      };

      // 构建查询字符串
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      wx.request({
        url: `${apiUrl}?${queryString}`,
        method: 'GET',
        success: (res) => {
          console.log('身份证核验API响应:', res);

          if (res.statusCode === 200 && res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '身份证核验失败'));
          }
        },
        fail: (error) => {
          console.error('身份证核验请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 更新用户等级
  updateUserLevel: function() {
    return new Promise((resolve, reject) => {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !userInfo.account) {
        reject(new Error('用户信息不完整'));
        return;
      }

      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      const apiUrl = `${app.globalData.apiConfig.baseUrl}/api/user/account/${userInfo.account}/user-level/1`;

      wx.request({
        url: apiUrl,
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('更新用户等级API响应:', res);

          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error('更新用户等级失败'));
          }
        },
        fail: (error) => {
          console.error('更新用户等级请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  }
});