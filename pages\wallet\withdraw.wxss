/* pages/wallet/withdraw.wxss */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 提现金额部分 */
.withdraw-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.withdraw-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.withdraw-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.withdraw-balance {
  font-size: 28rpx;
  color: #666;
}

.amount-input-section {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.currency-symbol {
  font-size: 48rpx;
  color: #333;
  margin-right: 20rpx;
}

.amount-input {
  flex: 1;
  font-size: 48rpx;
  color: #333;
}

.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-amount {
  background: #f8f8f8;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 图片上传部分 */
.image-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.image-upload-container {
  margin-top: 20rpx;
}

.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.image-preview {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.image-actions {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-top: 20rpx;
}

.action-btn {
  padding: 12rpx 30rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ddd;
  color: #666;
  background-color: #fff;
}

.action-btn.delete {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 备注信息部分 */
.remark-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  margin-top: -10rpx;
}

.remark-input-container {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #fafafa;
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  background: transparent;
}

/* 提现说明部分 */
.withdraw-notice {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.notice-content {
  color: #666;
}

.notice-item {
  font-size: 26rpx;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 提交按钮部分 */
.submit-section {
  padding: 40rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.disabled {
  background: #ccc;
  color: #fff;
} 