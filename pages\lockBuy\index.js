const app = getApp();

Page({
  data: {
    buyPrice: '--', // 今日买入价
    weight: '', // 买入克重
    estimatedAmount: '0.00', // 预估金额
    serviceFee: '0.00', // 总服务费
    totalPayment: '0.00', // 应付金额
    selectedAddress: null, // 选中的收货地址
    agreementChecked: false, // 协议是否勾选
    canBuy: false // 是否可以立即买入
  },

  onLoad() {
    // 加载今日买入价
    this.loadBuyPrice();
  },

  // 加载今日买入价
  loadBuyPrice() {
    wx.showLoading({
      title: '加载金价...',
      mask: true
    });

    // 使用与首页一致的金价API
    wx.request({
      url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 200 && res.data && res.data.code === 1 && res.data.data && res.data.data.list) {
          const goldData = res.data.data.list;
          const mainGold = goldData.Au9999 || {};
          const buyPrice = mainGold.buyprice || '--'; // 买入金价

          this.setData({
            buyPrice: buyPrice
          });
          // 加载完价格后重新计算金额
          this.calculateAmount();
        } else {
          console.error('获取买入价失败', res);
          wx.showToast({
            title: '获取金价失败',
            icon: 'none'
          });
          this.setData({
            buyPrice: '--'
          });
          this.calculateAmount(); // 即使失败也尝试计算一次，更新按钮状态
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求金价API失败', err);
        wx.showToast({
          title: '网络错误，获取金价失败',
          icon: 'none'
        });
        this.setData({
          buyPrice: '--'
        });
        this.calculateAmount(); // 即使失败也尝试计算一次，更新按钮状态
      }
    });
  },

  // 买入克重输入
  onWeightInput(e) {
    const weight = e.detail.value;
    this.setData({
      weight: weight
    });
    this.calculateAmount();
  },

  // 计算预估金额、服务费和应付金额
  calculateAmount() {
    const weight = parseFloat(this.data.weight);
    const price = parseFloat(this.data.buyPrice);

    if (isNaN(weight) || weight <= 0 || isNaN(price) || price <= 0) { // 价格为--时也重置
      this.setData({
        estimatedAmount: '0.00',
        serviceFee: '0.00',
        totalPayment: '0.00',
        canBuy: false
      });
      return;
    }

    // TODO: 根据实际业务逻辑计算服务费和应付金额
    const estimatedAmount = weight * price;
    const serviceFee = estimatedAmount * 0.005; // 假设买金服务费率0.5%
    const totalPayment = estimatedAmount + serviceFee; // 应付金额 = 预估金额 + 服务费

    this.setData({
      estimatedAmount: estimatedAmount.toFixed(2),
      serviceFee: serviceFee.toFixed(2),
      totalPayment: totalPayment.toFixed(2),
      canBuy: this.checkCanBuy(weight, this.data.selectedAddress, this.data.agreementChecked)
    });
  },

  // 选择收货地址
  selectAddress() {
    wx.navigateTo({
      url: '/pages/address/index?from=lockBuy' // 跳转到地址列表页面，并带上来源标识
    });
  },

  // 从地址列表页面返回时接收选中的地址数据
  onShow() {
    // 小程序页面栈机制，从地址选择页面返回时会执行onShow
    // 检查全局变量或页面栈中传递的 selectedAddress 数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const selectedAddress = currentPage.data.selectedAddress; // 从当前页面data中获取地址选择页面设置的数据

    if (selectedAddress) {
       // 确认地址数据格式是否与后端list接口返回一致
       // 假设地址数据格式包含 addressId, receiverName, receiverPhone, detailAddress 等字段
       this.setData({
         selectedAddress: selectedAddress,
       });
       // 重新检查是否可以买入，因为地址已经选中
       this.setData({
         canBuy: this.checkCanBuy(parseFloat(this.data.weight), selectedAddress, this.data.agreementChecked)
       });
    } else {
      // 如果没有传递地址回来，可能是第一次进入或取消选择
      this.setData({
        selectedAddress: null, // 确保没有残留的地址数据
        canBuy: this.checkCanBuy(parseFloat(this.data.weight), null, this.data.agreementChecked)
      });
    }
    // 重新计算金额以更新按钮状态 (例如：价格加载完成后调用)
    // this.calculateAmount(); // loadBuyPrice成功回调里已经调用，这里不需要重复调用
  },

  // 协议勾选变化
  onAgreementChange(e) {
    const checked = e.detail.value.length > 0;
    this.setData({
      agreementChecked: checked,
      canBuy: this.checkCanBuy(parseFloat(this.data.weight), this.data.selectedAddress, checked)
    });
  },

  // 检查是否可以立即买入
  checkCanBuy(weight, address, agreementChecked) {
    return weight > 0 && 
           address !== null && 
           agreementChecked;
  },

  // 提交买入订单
  submitBuy() {
    if (!this.data.canBuy) return;

    // TODO: 调用真实的后端 API 提交买入订单
    // 需要后端提供 API 地址、请求方法 (POST/PUT)、请求头和请求体结构
    // 模拟 API 请求
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 准备 API 请求数据 (根据模拟或将来真实的后端 API 文档构建)
    const apiData = {
      weight: parseFloat(this.data.weight), // 买入克重
      addressId: this.data.selectedAddress.addressId, // 收货地址ID
      // totalAmount: parseFloat(this.data.totalPayment), // 订单总金额 (可能需要)
      // 其他可能需要的字段，如用户ID，应付金额等，通常用户ID后端会从token中获取
    };

    console.log('模拟提交买入订单数据:', apiData);

    // 模拟 API 请求成功或失败的场景
    setTimeout(() => {
      wx.hideLoading();
      // 模拟成功
      wx.showModal({
        title: '提交成功',
        content: '买入订单已提交，请在订单列表查看详情。',
        showCancel: false,
        success: () => {
          // 提交成功后跳转到订单列表或其他页面
          // wx.redirectTo({ url: '/pages/orderList/index' }); // 假设有订单列表页面
          wx.navigateBack(); // 或者返回上一页
        }
      });
      // 模拟失败
      // wx.showModal({
      //   title: '提交失败',
      //   content: '订单提交失败，请稍后重试。',
      //   showCancel: false,
      // });

    }, 2000); // 模拟网络延迟 2 秒
  }
}) 