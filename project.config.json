{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "黄金回收销售裂变小程序", "setting": {"useCompilerPlugins": ["sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "minified": true, "postcss": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "", "appid": "wx3f0e971d228e8ee5", "libVersion": "2.30.2", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}