/* pages/customerService/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 客服二维码区域 */
.qr-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.qr-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 20rpx;
  border: 2rpx solid #eee;
  margin-bottom: 20rpx;
}

.qr-tip {
  font-size: 28rpx;
  color: #666;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

/* 联系提示区域 */
.contact-tips {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tip-item:last-child {
  border-bottom: none;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.tip-content {
  flex: 1;
}

.tip-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.tip-value {
  font-size: 26rpx;
  color: #FF4D4F;
  font-weight: bold;
}

.tip-placeholder {
  font-size: 24rpx;
  color: #999;
}

.tip-copy {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 常见问题区域 */
.faq-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.faq-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.faq-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.faq-arrow {
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.faq-arrow.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 15rpx;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.show {
  max-height: 200rpx;
}

/* 底部提示 */
.bottom-tips {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.service-time-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.service-time-content {
  font-size: 26rpx;
  color: #FF4D4F;
  font-weight: bold;
}
