# 返点分佣系统说明

## 功能概述

返点分佣系统用于在黄金回收订单完成时自动给上级和上上级用户进行返点。当订单状态变为"已完成"时，系统会根据用户关系和费率差异自动计算并发放返点。

## 核心文件

### 1. API接口文件 (`src/api/rebate.js`)
新增的API函数：
- `getUserRelationFee(account)` - 获取用户关系费率信息
- `getUserDetails(account)` - 获取用户详情
- `getPlatformFeeRate(userLevel)` - 获取平台手续费率
- `depositToAccount(accountId, data)` - 账户充值

### 2. 工具函数文件 (`src/utils/rebate.js`)
核心函数：
- `processRebate(account, weight)` - 处理返点分佣的主要逻辑
- `processRebateWithLoading(account, weight, setLoading)` - 带加载状态的返点处理

### 3. 集成页面
- `src/views/orders/recycle.vue` - 订单列表页面
- `src/views/orders/detail.vue` - 订单详情页面

## 业务逻辑流程

### 1. 触发条件
返点分佣在以下两种情况下触发：
- 点击"完成订单"按钮（订单状态从5变为6）
- 点击"已送到店"按钮（订单状态从1直接变为6）

### 2. 计算规则
- **上级用户返点** = (当前用户费率 - 上级用户费率) × 订单重量
- **上上级用户返点** = (当前用户费率 - 上级用户费率 - 上上级用户费率) × 订单重量

### 3. 处理步骤
1. 获取用户关系费率信息（`/api/admin/user-relation-fee/{account}`）
2. 获取当前用户详情，主要获取userLevel（`/api/admin/users/{account}`）
3. 获取当前用户的手续费率（`/api/platform-fees/{userLevel}`）
4. 计算返点金额
5. 为有效的上级用户充值（`/api/accounts/account/{accountId}/deposit`）

## API接口说明

### 用户关系费率接口
```
GET /api/admin/user-relation-fee/{account}
```
返回数据结构：
```json
{
  "parentId": "上级用户ID",
  "parentFeeRate": 上级用户费率,
  "grandParentId": "上上级用户ID", 
  "grandParentFeeRate": 上上级用户费率
}
```

### 用户详情接口
```
GET /api/admin/users/{account}
```
返回数据结构：
```json
{
  "account": "用户账号",
  "userLevel": 用户等级
}
```

### 平台费率接口
```
GET /api/platform-fees/{userLevel}
```
返回数据结构：
```json
{
  "feeRate": 手续费率
}
```

### 账户充值接口
```
POST /api/accounts/account/{accountId}/deposit
```
请求数据：
```json
{
  "amount": 充值金额,
  "transactionId": "交易ID（时间戳）",
  "remark": "备注信息"
}
```

## 异常处理

系统包含以下异常处理机制：

1. **无上级用户** - 当parentId为空或null时，跳过返点处理
2. **无上上级用户** - 当grandParentId为空或null时，跳过上上级返点
3. **返点金额异常** - 当计算出的返点金额为负数或零时，不进行充值
4. **API调用失败** - 记录错误日志并显示用户友好的错误提示
5. **参数验证** - 验证账号和重量参数的有效性

## 日志记录

系统会记录以下关键信息：
- 返点处理开始和结束
- 用户关系费率信息
- 返点金额计算过程
- API调用结果
- 异常情况

## 使用示例

在订单处理函数中调用返点处理：

```javascript
// 处理返点分佣
if (orderData.account && orderData.estimatedWeight) {
  await processRebateWithLoading(
    orderData.account, 
    orderData.estimatedWeight, 
    (loading) => {
      console.log('返点处理loading状态:', loading)
    }
  )
}
```

## 更新日志

### 2025-06-19 - 新增其他项目字段

根据创建报价接口文档，在报价弹窗中新增了 `otherItems` 字段：

#### 修改内容：
1. **API接口更新**：创建报价接口新增 `otherItems` 字段（其他项目）
2. **UI界面更新**：
   - `recycle.vue` 和 `detail.vue` 的检测弹窗中添加"其他项目"文本域
   - 支持最多500字符输入，带字数统计
   - 字段为可选项，不影响现有验证逻辑
3. **数据结构更新**：
   - `inspectionForm` 中添加 `otherItems: ''` 字段
   - 提交报价数据时包含 `otherItems` 字段

#### 字段说明：
- **字段名称**：otherItems
- **字段类型**：string
- **是否必填**：否
- **最大长度**：500字符
- **用途**：记录除标准检测项目外的其他相关信息

## 注意事项

1. 返点处理是异步操作，不会阻塞订单状态更新
2. 返点失败不会影响订单完成流程
3. 所有返点记录都会生成唯一的交易ID（基于时间戳）
4. 返点备注统一格式为："{当前用户账号}返点"
5. 系统会自动跳过无效的返点（金额≤0）

## 测试建议

1. 测试无上级用户的情况
2. 测试返点金额为负数的情况
3. 测试API调用失败的情况
4. 测试正常返点流程
5. 验证返点金额计算的准确性
6. **新增**：测试其他项目字段的输入和提交
