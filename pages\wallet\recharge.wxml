<!--pages/wallet/recharge.wxml-->
<view class="container">

  <!-- 客服二维码 -->
  <view class="qrcode-section">
    <view class="qrcode-title">请添加客服微信进行充值</view>
    <view class="qrcode-subtitle">长按二维码保存图片，打开微信扫一扫添加</view>
    <view class="qrcode-container">
      <image class="qrcode-image" src="/images/customer-service-qr.jpg" mode="aspectFit" show-menu-by-longpress></image>
    </view>
    <view class="qrcode-tips">添加客服后，请备注"充值"</view>
  </view>

  <!-- 充值表单 -->
  <view class="form-section">
    <view class="form-title">填写充值信息</view>
    
    <view class="form-item">
      <view class="form-label">充值金额</view>
      <input class="form-input" type="digit" placeholder="请输入充值金额" bindinput="inputAmount" value="{{amount}}"/>
    </view>
    
    <view class="form-item">
      <view class="form-label">手机号码</view>
      <input class="form-input" type="number" placeholder="请输入手机号码" bindinput="inputPhone" value="{{phone}}"/>
    </view>
    
    <view class="form-item">
      <view class="form-label">转账截图</view>
      <view class="upload-container">
        <view class="image-preview" wx:if="{{imageUrl}}">
          <image class="preview-image" src="{{imageUrl}}" mode="aspectFill" bindtap="previewImage"></image>
          <view class="delete-icon" catchtap="deleteImage">×</view>
        </view>
        <view class="upload-button" bindtap="chooseImage" wx:else>
          <view class="upload-icon">+</view>
          <view class="upload-text">上传截图</view>
        </view>
      </view>
      <view class="upload-tips">请上传转账给客服的截图凭证</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">备注信息</view>
      <textarea class="form-textarea" placeholder="可选填写备注信息" bindinput="inputRemark" value="{{remark}}"></textarea>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-button {{submitDisabled ? 'disabled' : ''}}" bindtap="submitRecharge">
    {{submitting ? '提交中...' : '提交充值申请'}}
  </view>
</view>
