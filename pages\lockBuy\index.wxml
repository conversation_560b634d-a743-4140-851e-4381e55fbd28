<!--pages/lockBuy/index.wxml-->
<view class="container">
  <!-- 顶部买入价 -->
  <view class="price-section">
    <text class="price-label">今日买入价:</text>
    <text class="buy-price">{{buyPrice}} 元/克</text>
  </view>

  <!-- 买入信息 -->
  <view class="buy-info-section">
    <view class="section-title">买入信息</view>
    <view class="info-item">
      <text class="item-label">买入克重:</text>
      <input class="item-input" type="digit" placeholder="请输入克重" value="{{weight}}" bindinput="onWeightInput" />
      <text class="item-unit">克</text>
    </view>
    <view class="info-item">
      <text class="item-label">预估金额:</text>
      <text class="item-value">{{estimatedAmount}} 元</text>
    </view>
    <view class="info-item">
      <text class="item-label">总服务费:</text>
      <text class="item-value">{{serviceFee}} 元</text>
    </view>
    <view class="info-item">
      <text class="item-label">应付金额:</text>
      <text class="item-value">{{totalPayment}} 元</text>
    </view>
  </view>

  <!-- 收货地址 -->
  <view class="address-section">
    <view class="section-title">收货地址</view>
    <view class="address-selector" bindtap="selectAddress">
      <block wx:if="{{selectedAddress}}">
        <view class="address-info">
          <text class="receiver-name">{{selectedAddress.receiverName}}</text>
          <text class="receiver-phone">{{selectedAddress.receiverPhone}}</text>
        </view>
        <view class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detailAddress}}</view>
      </block>
      <block wx:else>
        <text class="placeholder">请选择收货地址</text>
      </block>
      <text class="select-arrow">></text>
    </view>
  </view>

  <!-- 锁价买金流程 -->
  <view class="process-section">
    <view class="section-title">锁价买金流程</view>
    <!-- 流程内容，可以根据实际流程添加步骤描述或图片 -->
    <view class="process-content">
      <view>1. 输入买入克重并确认信息</view>
      <view>2. 选择收货地址</view>
      <view>3. 支付应付金额并锁价</view>
      <view>4. 平台发货</view>
      <view>5. 确认收货</view>
      <view>6. 完成交易</view>
    </view>
  </view>

  <!-- 底部固定区域 -->
  <view class="bottom-bar">
    <view class="agreement-checkbox">
      <checkbox-group bindchange="onAgreementChange">
        <label>
          <checkbox value="agree" checked="{{agreementChecked}}" />我已阅读并同意 <text class="agreement-link">《贵金属购销服务协议》</text>
        </label>
      </checkbox-group>
    </view>
    <button class="buy-button {{canBuy ? '' : 'disabled'}}" bindtap="submitBuy" disabled="{{!canBuy}}">立即买入</button>
  </view>
</view> 