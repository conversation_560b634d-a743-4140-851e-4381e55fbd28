/* pages/wallet/index.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding-bottom: 30rpx;
}

/* 页面标题 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 40rpx;
  color: #333;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.right-icons {
  width: 60rpx;
}

/* 钱包卡片 */
.wallet-card {
  margin: 20rpx 30rpx;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(212, 56, 13, 0.2);
}

.wallet-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.wallet-card-title {
  font-size: 28rpx;
}

.wallet-card-action {
  font-size: 26rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.wallet-card-balance {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.wallet-card-actions {
  display: flex;
  justify-content: space-between;
}

.wallet-action-btn {
  width: 48%;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.9);
  color: #FF4D4F;
}

.recharge-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* 资产明细 */
.assets-section {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.assets-header {
  margin-bottom: 20rpx;
}

.assets-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.assets-list {
  display: flex;
  flex-direction: column;
}

.asset-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.asset-item:last-child {
  border-bottom: none;
}

.asset-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
  margin-right: 20rpx;
}



.points-icon {
  background-color: #FFA940;
}

.gold-icon {
  background-color: #FAAD14;
}

.asset-info {
  flex: 1;
}

.asset-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.asset-value {
  font-size: 24rpx;
  color: #666;
}

.asset-arrow {
  width: 30rpx;
  height: 30rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGV2cm9uLXJpZ2h0Ij48cG9seWxpbmUgcG9pbnRzPSI5IDE4IDE1IDEyIDkgNiI+PC9wb2x5bGluZT48L3N2Zz4=');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 交易记录 */
.transaction-section {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.transaction-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.transaction-filter {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.filter-arrow {
  font-size: 20rpx;
  margin-left: 8rpx;
}

.transaction-list {
  display: flex;
  flex-direction: column;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
  margin-right: 20rpx;
}

.income-icon {
  background-color: #52C41A;
}

.expense-icon {
  background-color: #FF4D4F;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
  font-weight: normal;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-amount {
  font-size: 30rpx;
  font-weight: bold;
}

.income-amount {
  color: #52C41A;
}

.expense-amount {
  color: #FF4D4F;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
