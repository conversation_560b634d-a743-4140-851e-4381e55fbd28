// pages/lockPrice/lockPrice.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isLogin: false,
    balance: '0', // 可用定金
    points: '0', // 可用积分
    goldInventory: '--', // 黄金库存，现在从API获取，初始化为--
    goldPriceBuy: '--', // 买入价格，初始化为--
    goldPriceSell: '--', // 卖出价格，初始化为--
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查本地存储中是否有用户信息
    this.checkLoginStatus(); // 使用统一的检查登录状态方法
    
    // 获取金价信息
    this.getGoldPrice();
    // 获取钱包信息
    this.getWalletInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查用户登录状态
    this.checkLoginStatus(); // 页面显示时也检查登录状态

    // 刷新金价信息
    this.getGoldPrice();
    // 刷新钱包信息 (登录状态变化时可能需要刷新)
    this.getWalletInfo();
  },

  // 检查用户登录状态和更新用户信息
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (userInfo && token) {
      app.globalData.userInfo = userInfo;
      this.setData({
        userInfo: userInfo,
        isLogin: true
      });
    } else {
      this.setData({
        isLogin: false,
        userInfo: null,
        balance: '0',
        points: '0',
      });
    }
  },

  /**
   * 获取金价信息
   */
  getGoldPrice() {
    wx.showLoading({
      title: '加载金价...',
      mask: true
    });

    wx.request({
      url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 200 && res.data && res.data.code === 1 && res.data.data && res.data.data.list) {
          const goldData = res.data.data.list;
          const mainGold = goldData.Au9999 || {};

          this.setData({
            goldPriceBuy: mainGold.buyprice || '--', // 买入价格
            goldPriceSell: mainGold.sellprice || '--', // 卖出价格
            goldInventory: mainGold.reserve || '--' // 假设 reserve 是黄金库存字段
          });
        } else {
          console.error('获取金价数据失败', res);
          wx.showToast({
            title: '获取金价失败',
            icon: 'none'
          });
           this.setData({
            goldPriceBuy: '--',
            goldPriceSell: '--',
            goldInventory: '--'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求金价API失败', err);
        wx.showToast({
          title: '网络错误，获取金价失败',
          icon: 'none'
        });
         this.setData({
          goldPriceBuy: '--',
          goldPriceSell: '--',
          goldInventory: '--'
        });
      }
    });
  },

  /**
   * 获取钱包信息
   */
  getWalletInfo() {
    if (!this.data.isLogin || !this.data.userInfo || !this.data.userInfo.account) return;

    const accountId = this.data.userInfo.account;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${accountId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        if (res.data.code === 200) {
          const accountData = res.data.data;
          this.setData({
            balance: accountData.balance !== undefined ? accountData.balance.toFixed(2) : '0',
            points: accountData.totalRebate !== undefined ? accountData.totalRebate.toFixed(2) : '0',
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取钱包信息失败',
            icon: 'none'
          });
           this.setData({
            balance: '0',
            points: '0',
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
         this.setData({
          balance: '0',
          points: '0',
        });
      }
    });
  },

  /**
   * 锁价买料
   */
  lockBuy() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (!userInfo || !token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            this.goToLogin();
          }, 1500);
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/lockBuy/index'
    });
  },

  /**
   * 锁价卖料
   */
  lockSell() {
     const userInfo = wx.getStorageSync('userInfo');
     const token = wx.getStorageSync('token');
    
     if (!userInfo || !token) {
       wx.showToast({
         title: '请先登录',
         icon: 'none',
         duration: 2000,
         success: () => {
           setTimeout(() => {
             this.goToLogin();
           }, 1500);
         }
       });
       return;
     }

    wx.navigateTo({
      url: '/pages/lockSell/index' // 跳转到锁价卖料页面
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '黄金回收销售，锁定最优价格',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  }
})