/* pages/customer/index.wxss */
page {
  --primary-color: #4A1010;
  --secondary-color: #6B1919;
  --primary-gradient: linear-gradient(135deg, #6B1919, #4A1010);
  --text-color: #333333;
  --light-text-color: #666666;
  --background-color: #F8F0F0;
  --border-color: #D9C1C1;
  background-color: var(--background-color);
}

.container {
  padding-bottom: 30rpx;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 60rpx 0 20rpx;
  font-size: 28rpx;
}

.search-icon, .clear-icon {
  position: absolute;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 32rpx;
  height: 32rpx;
}

.icon-text {
  font-size: 32rpx;
  color: #999;
}

/* 客户统计 */
.customer-stats {
  display: flex;
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10rpx;
  bottom: 10rpx;
  width: 1rpx;
  background-color: #eee;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: var(--background-color);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(to right, #FF4D4F, #D4380D);
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: var(--primary-color);
  font-weight: bold;
}

/* 客户列表 */
.customer-list {
  padding: 0 20rpx;
}

.customer-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  margin-left: -20rpx;
  margin-right: -20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-avatar {
  margin-right: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #eee;
}

.customer-info {
  flex: 1;
}

.customer-name-level {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.customer-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.customer-level {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
}

.customer-level.level1 {
  background: linear-gradient(to right, #52C41A, #389E0D);
}

.customer-level.level2 {
  background: linear-gradient(to right, #1890FF, #096DD9);
}

.customer-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.customer-phone, .customer-time {
  font-size: 24rpx;
  color: #999;
}

.customer-stats {
  display: flex;
  justify-content: space-between;
}

.customer-stat-item {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  color: var(--primary-color);
  font-weight: bold;
}

.customer-arrow {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 30rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 客户详情弹窗 */
.customer-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.customer-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.customer-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  overflow-y: auto;
  flex: 1;
}

/* 详情样式 */
.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.detail-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.detail-basic {
  flex: 1;
}

.detail-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.detail-level {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-bottom: 10rpx;
}

.detail-level.level1 {
  background: linear-gradient(to right, #52C41A, #389E0D);
}

.detail-level.level2 {
  background: linear-gradient(to right, #1890FF, #096DD9);
}

.detail-phone {
  font-size: 26rpx;
  color: #666;
}

.detail-section {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.detail-value.highlight {
  color: var(--primary-color);
  font-weight: bold;
}

/* 订单列表 */
.order-list {
  margin-top: 15rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-id {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-amount {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: bold;
}

.no-order {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}
