<!--pages/activity/activity.wxml-->
<view class="container">

  <!-- 秒杀活动区域 -->
  <view class="seckill-section">
    <view class="section-header">
      <view class="section-title-wrap">
        <image class="section-icon" src="/images/icons/seckill.png"></image>
        <text class="section-title">限时秒杀</text>
      </view>
      <view class="countdown-container">
        <text class="countdown-label">距结束</text>
        <view class="countdown-timer">
          <text class="countdown-block">{{countdownHours}}</text>
          <text class="countdown-colon">:</text>
          <text class="countdown-block">{{countdownMinutes}}</text>
          <text class="countdown-colon">:</text>
          <text class="countdown-block">{{countdownSeconds}}</text>
        </view>
      </view>
    </view>

    <scroll-view class="seckill-scroll" scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
      <view class="seckill-products">
        <view class="seckill-product" wx:for="{{seckillProducts}}" wx:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
          <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
          <view class="product-info">
            <view class="product-name ellipsis">{{item.name}}</view>
            <view class="product-price-row">
              <view class="product-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{item.seckillPrice}}</text>
              </view>
              <view class="original-price">¥{{item.originalPrice}}</view>
            </view>
            <view class="product-progress">
              <view class="progress-bar">
                <view class="progress-inner" style="width: {{item.soldPercent}}%;"></view>
              </view>
              <view class="progress-text">已抢{{item.soldPercent}}%</view>
            </view>
            <view class="offline-tag">仅限线下门店领取</view>
            <button class="seckill-btn {{item.isSoldOut ? 'sold-out' : ''}}" catchtap="handleSeckill" data-id="{{item.id}}" disabled="{{item.isSoldOut}}">
              {{item.isSoldOut ? '已抢光' : '立即抢购'}}
            </button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 秒杀成功弹窗 -->
  <view class="modal {{showSuccessModal ? 'show' : ''}}">
    <view class="modal-mask" bindtap="closeSuccessModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">抢购成功</text>
        <view class="modal-close" bindtap="closeSuccessModal">×</view>
      </view>
      <view class="modal-body">
        <image class="success-icon" src="/images/icons/success.png"></image>
        <view class="success-text">恭喜您，抢购成功！</view>
        <view class="success-tips">请凭订单号到线下门店领取商品</view>
        <view class="order-number">订单号：{{currentOrderId}}</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeSuccessModal">关闭</button>
        <button class="modal-btn confirm" bindtap="viewOrderDetail">查看订单</button>
      </view>
    </view>
  </view>
</view>