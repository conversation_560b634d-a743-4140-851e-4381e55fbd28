/* pages/lockSell/index.wxss */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 150rpx; /* 为底部固定按钮留出空间 */
}

.price-section {
  background: linear-gradient(to right, #f8b75c, #f89a7b);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.repurchase-price {
  margin-left: 10rpx;
}

.sell-info-section,
.address-section,
.process-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 8rpx solid #ff7e5f;
  padding-left: 10rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: #666;
}

.item-input {
  flex: 1;
  text-align: right;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #333;
}

.item-unit {
  font-size: 28rpx;
  color: #333;
}

.item-value {
  font-size: 28rpx;
  color: #ff7e5f;
  font-weight: bold;
}

.address-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.address-selector .placeholder {
  color: #999;
  font-size: 28rpx;
}

.address-info {
  flex: 1;
  margin-right: 20rpx;
}

.receiver-name,
.receiver-phone {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  margin-top: 8rpx;
}

.select-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 4rpx solid #999;
  border-right: 4rpx solid #999;
  transform: rotate(45deg);
}

.process-content view {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 100;
}

.agreement-checkbox {
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.agreement-link {
  color: #1890ff;
}

.sell-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.sell-button.disabled {
  background: #ccc;
  color: #fff;
} 