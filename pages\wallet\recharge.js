// pages/wallet/recharge.js
const app = getApp();

Page({
  data: {
    amount: '', // 充值金额
    phone: '', // 手机号码
    imageUrl: '', // 转账截图
    remark: '', // 备注信息
    submitting: false, // 是否正在提交
    submitDisabled: true // 是否禁用提交按钮
  },

  onLoad: function (options) {
    // 如果用户已登录，自动填充手机号
    if (app.globalData.userInfo && app.globalData.userInfo.phone) {
      this.setData({
        phone: app.globalData.userInfo.phone
      });
    }

    // 检查表单是否可提交
    this.checkFormValid();
  },

  // 输入充值金额
  inputAmount: function(e) {
    this.setData({
      amount: e.detail.value
    });
    this.checkFormValid();
  },

  // 输入手机号码
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
    this.checkFormValid();
  },

  // 输入备注信息
  inputRemark: function(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          imageUrl: tempFilePath
        });
        this.checkFormValid();
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage: function() {
    wx.previewImage({
      urls: [this.data.imageUrl],
      current: this.data.imageUrl
    });
  },

  // 删除图片
  deleteImage: function() {
    this.setData({
      imageUrl: ''
    });
    this.checkFormValid();
  },

  // 检查表单是否有效
  checkFormValid: function() {
    const { amount, phone, imageUrl } = this.data;
    const isValid = amount && phone && imageUrl && this.isValidPhone(phone);

    this.setData({
      submitDisabled: !isValid
    });
  },

  // 验证手机号
  isValidPhone: function(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  },

  // 提交充值申请
  submitRecharge: function() {
    if (this.data.submitDisabled || this.data.submitting) {
      return;
    }

    // 表单验证
    if (!this.data.amount) {
      wx.showToast({
        title: '请输入充值金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.phone) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    if (!this.isValidPhone(this.data.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请上传转账截图',
        icon: 'none'
      });
      return;
    }

    // 设置提交中状态
    this.setData({
      submitting: true
    });

    // 直接提交充值申请
    this.submitRechargeRequest();
  },

  // 提交充值请求
  submitRechargeRequest: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      wx.showToast({
        title: '用户信息异常，请重新登录',
        icon: 'none'
      });
      this.setData({
        submitting: false
      });
      return;
    }

    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    // 先上传图片到minio
    this.uploadImageToMinio(this.data.imageUrl)
      .then((imageUrl) => {
        wx.showLoading({
          title: '提交申请中...',
          mask: true
        });

        // 图片上传成功后，提交充值申请
        return this.submitRechargeWithImageUrl(imageUrl, userInfo.account);
      })
      .then(() => {
        wx.hideLoading();
        this.setData({
          submitting: false
        });

        wx.showModal({
          title: '提交成功',
          content: '充值申请已提交，等待审核',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('充值申请失败:', error);

        this.setData({
          submitting: false
        });

        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        });
      });
  },

  // 上传图片到minio
  uploadImageToMinio: function(filePath) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      wx.uploadFile({
        url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          console.log('图片上传API响应:', res);

          try {
            const responseData = JSON.parse(res.data);
            console.log('解析后的响应数据:', responseData);

            if (responseData.code === 200) {
              const imageUrl = responseData.data;
              console.log('图片上传成功，URL:', 'https://www.iejhsgold.cn'+imageUrl);
              resolve('https://www.iejhsgold.cn'+imageUrl);
            } else {
              console.error('图片上传失败，API返回:', responseData);
              reject(new Error(responseData.message || '图片上传失败'));
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            reject(new Error('解析响应失败'));
          }
        },
        fail: (error) => {
          console.error('图片上传请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 使用图片URL提交充值申请
  submitRechargeWithImageUrl: function(transferImageUrl, accountId) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/account-review/submit`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        data: {
          transactionType: 1, // 1=充值
          accountId: accountId,
          amount: this.data.amount,
          remark: this.data.remark || '',
          paymentType: 2, // 固定设置为2
          transferImage: transferImageUrl
        },
        success: (res) => {
          console.log('充值申请API响应:', res);

          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.message || '提交失败'));
          }
        },
        fail: (error) => {
          console.error('充值申请请求失败:', error);
          reject(new Error('网络错误，请重试'));
        }
      });
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});