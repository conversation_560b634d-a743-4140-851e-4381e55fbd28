/* pages/lockBuy/index.wxss */
@import "../lockSell/index.wxss"; /* 导入卖料页面的基础样式 */

/* 根据买金页面调整样式 */
.price-section {
  background: linear-gradient(to right, #8cb8ff, #67c3ee); /* 买金价格区域使用蓝色系渐变 */
}

.section-title {
  border-left-color: #50a1ff; /* 标题左侧边框颜色 */
}

.item-value {
  color: #50a1ff; /* 信息项数值颜色 */
}

.buy-button {
  background: linear-gradient(to right, #50a1ff, #2b8aff); /* 立即买入按钮使用蓝色系渐变 */
}

.buy-button.disabled {
  background: #ccc;
  color: #fff;
} 