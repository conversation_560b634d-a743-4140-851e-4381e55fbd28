// pages/commission/index.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    commission: 0,
    commissionList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    currentTab: 0, // 0: 佣金明细, 1: 提现记录
    withdrawList: [],
    withdrawLoading: false,
    withdrawHasMore: true,
    withdrawPage: 1,
    showWithdrawModal: false,
    withdrawAmount: '',
    maxWithdrawAmount: 0
  },

  onLoad: function (options) {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        commission: app.globalData.userInfo.commission || 0,
        maxWithdrawAmount: app.globalData.userInfo.commission || 0
      });
      
      this.loadCommissionList();
    } else {
      // 未登录，跳转到个人中心
      wx.switchTab({
        url: '/pages/my/my'
      });
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    Promise.all([
      this.loadUserInfo(),
      this.data.currentTab === 0 ? this.loadCommissionList(true) : this.loadWithdrawList(true)
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 上拉加载更多
    if (this.data.currentTab === 0) {
      if (this.data.hasMore && !this.data.loading) {
        this.loadCommissionList(false);
      }
    } else {
      if (this.data.withdrawHasMore && !this.data.withdrawLoading) {
        this.loadWithdrawList(false);
      }
    }
  },

  // 加载用户信息
  loadUserInfo: function () {
    return new Promise((resolve) => {
      if (!app.globalData.openid) {
        resolve();
        return;
      }
      
      const db = wx.cloud.database();
      
      db.collection('users')
        .doc(app.globalData.openid)
        .get()
        .then(res => {
          app.globalData.userInfo = res.data;
          
          this.setData({
            userInfo: res.data,
            commission: res.data.commission || 0,
            maxWithdrawAmount: res.data.commission || 0
          });
          
          resolve();
        })
        .catch(() => {
          resolve();
        });
    });
  },

  // 加载佣金明细
  loadCommissionList: function (refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        commissionList: []
      });
    }

    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return new Promise((resolve) => {
      const db = wx.cloud.database();
      
      // 查询订单中的佣金记录
      db.collection('orders')
        .where({
          'commissions.userId': app.globalData.openid
        })
        .skip((this.data.page - 1) * this.data.pageSize)
        .limit(this.data.pageSize)
        .orderBy('createTime', 'desc')
        .get()
        .then(res => {
          const orders = res.data;
          const commissionList = [];
          
          orders.forEach(order => {
            const commission = order.commissions.find(c => c.userId === app.globalData.openid);
            if (commission) {
              commissionList.push({
                orderId: order._id,
                amount: commission.amount,
                status: commission.status,
                level: commission.level,
                createTime: this.formatTime(order.createTime),
                orderStatus: order.status
              });
            }
          });
          
          this.setData({
            commissionList: this.data.commissionList.concat(commissionList),
            loading: false,
            hasMore: orders.length === this.data.pageSize,
            page: this.data.page + 1
          });
          
          resolve();
        })
        .catch(err => {
          console.error('获取佣金明细失败', err);
          this.setData({
            loading: false
          });
          resolve();
        });
    });
  },

  // 加载提现记录
  loadWithdrawList: function (refresh = false) {
    if (refresh) {
      this.setData({
        withdrawPage: 1,
        withdrawHasMore: true,
        withdrawList: []
      });
    }

    if (!this.data.withdrawHasMore || this.data.withdrawLoading) {
      return Promise.resolve();
    }

    this.setData({ withdrawLoading: true });

    return new Promise((resolve) => {
      const db = wx.cloud.database();
      
      // 查询提现记录
      db.collection('withdraws')
        .where({
          userId: app.globalData.openid
        })
        .skip((this.data.withdrawPage - 1) * this.data.pageSize)
        .limit(this.data.pageSize)
        .orderBy('createTime', 'desc')
        .get()
        .then(res => {
          const withdraws = res.data.map(item => {
            return {
              ...item,
              createTime: this.formatTime(item.createTime)
            };
          });
          
          this.setData({
            withdrawList: this.data.withdrawList.concat(withdraws),
            withdrawLoading: false,
            withdrawHasMore: withdraws.length === this.data.pageSize,
            withdrawPage: this.data.withdrawPage + 1
          });
          
          resolve();
        })
        .catch(err => {
          console.error('获取提现记录失败', err);
          this.setData({
            withdrawLoading: false
          });
          resolve();
        });
    });
  },

  // 切换标签
  changeTab: function (e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab
      });
      
      if (tab === 1 && this.data.withdrawList.length === 0) {
        this.loadWithdrawList();
      }
    }
  },

  // 显示提现面板
  showWithdraw: function () {
    this.setData({
      showWithdrawModal: true,
      withdrawAmount: ''
    });
  },

  // 隐藏提现面板
  hideWithdraw: function () {
    this.setData({
      showWithdrawModal: false
    });
  },

  // 输入提现金额
  inputWithdrawAmount: function (e) {
    this.setData({
      withdrawAmount: e.detail.value
    });
  },

  // 全部提现
  withdrawAll: function () {
    this.setData({
      withdrawAmount: (this.data.maxWithdrawAmount / 100).toFixed(2)
    });
  },

  // 提交提现申请
  submitWithdraw: function () {
    const amount = parseFloat(this.data.withdrawAmount) * 100;
    
    if (isNaN(amount) || amount <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      });
      return;
    }
    
    if (amount > this.data.maxWithdrawAmount) {
      wx.showToast({
        title: '提现金额不能超过可提现金额',
        icon: 'none'
      });
      return;
    }
    
    // 最低提现金额为1元
    if (amount < 100) {
      wx.showToast({
        title: '最低提现金额为1元',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '提交中...',
    });
    
    const db = wx.cloud.database();
    
    // 创建提现记录
    db.collection('withdraws')
      .add({
        data: {
          userId: app.globalData.openid,
          amount: amount,
          status: 'pending', // pending: 待审核, approved: 已通过, rejected: 已拒绝
          createTime: db.serverDate()
        }
      })
      .then(() => {
        // 更新用户佣金余额
        return db.collection('users').doc(app.globalData.openid).update({
          data: {
            commission: db.command.inc(-amount)
          }
        });
      })
      .then(() => {
        wx.hideLoading();
        
        // 更新本地数据
        this.setData({
          commission: this.data.commission - amount,
          maxWithdrawAmount: this.data.maxWithdrawAmount - amount,
          showWithdrawModal: false
        });
        
        // 更新全局数据
        if (app.globalData.userInfo) {
          app.globalData.userInfo.commission = this.data.commission;
        }
        
        // 刷新提现记录
        this.loadWithdrawList(true);
        
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('提现失败', err);
        wx.hideLoading();
        wx.showToast({
          title: '提现失败，请重试',
          icon: 'none'
        });
      });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    
    return `${year}-${this.formatNumber(month)}-${this.formatNumber(day)} ${this.formatNumber(hour)}:${this.formatNumber(minute)}`;
  },
  
  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  }
})
