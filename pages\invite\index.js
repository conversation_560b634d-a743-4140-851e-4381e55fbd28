// pages/invite/index.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    inviteCode: '',
    inviteCount: 0,
    commission: 0,
    inviteList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    // 上级用户相关
    superiorUser: null,
    superiorAccount: '',
    bindTime: ''
  },

  onLoad: function (options) {
    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      this.setData({
        userInfo: userInfo,
        inviteCode: userInfo.account || '',
        inviteCount: userInfo.inviteCount || 0,
        commission: userInfo.commission || 0
      });

      // 获取上级用户信息
      this.getSuperiorUser();
      this.loadInviteList();
    } else {
      // 未登录，跳转到登录页面
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
    }
  },

  onShow: function () {
    // 页面显示时刷新数据
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      this.setData({
        userInfo: userInfo,
        inviteCode: userInfo.account || ''
      });
      this.getSuperiorUser();
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    Promise.all([
      this.getSuperiorUser(),
      this.loadInviteList(true)
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadInviteList(false);
    }
  },

  onShareAppMessage: function () {
    return {
      title: '邀请好友，享受佣金',
      path: '/pages/index/index?inviter=' + (this.data.inviteCode || ''),
      imageUrl: '/images/invite-share.jpg'
    };
  },

  // 复制邀请码
  copyInviteCode: function () {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 输入上级账号
  inputSuperiorAccount: function (e) {
    this.setData({
      superiorAccount: e.detail.value
    });
  },

  // 获取上级用户信息
  getSuperiorUser: function () {
    return new Promise((resolve) => {
      const userInfo = this.data.userInfo;
      if (!userInfo || !userInfo.account) {
        resolve();
        return;
      }

      // 查询上级用户信息
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/user-relation/${userInfo.account}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          console.log('获取上级用户信息响应:', res);
          if (res.data.code === 200 && res.data.data) {
            const superiorData = res.data.data[0];
            this.setData({
              superiorUser: superiorData.parentId,
              bindTime: this.formatTime(superiorData.createTime)
            });
          } else {
            // 没有上级用户
            this.setData({
              superiorUser: null
            });
          }
          resolve();
        },
        fail: (error) => {
          console.error('获取上级用户信息失败:', error);
          this.setData({
            superiorUser: null
          });
          resolve();
        }
      });
    });
  },

  // 获取账户信息验证用户是否存在
  checkAccountExists: function (account) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/user/account/${account}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('获取账户信息响应:', res);
          if (res.data.code === 200) {
            if (res.data.data !== null && res.data.data !== undefined) {
              // 用户存在
              console.log('用户存在，账户信息:', res.data.data);
              resolve(res.data.data);
            } else {
              // 用户不存在
              console.log('用户不存在，data为:', res.data.data);
              reject(new Error('该用户不存在'));
            }
          } else {
            console.error('API返回错误:', res.data);
            reject(new Error(res.data.message || '查询失败'));
          }
        },
        fail: (error) => {
          console.error('获取账户信息网络请求失败:', error);
          reject(new Error('网络错误，请稍后重试'));
        }
      });
    });
  },

  // 绑定上级用户
  bindSuperior: function () {
    const { superiorAccount, userInfo } = this.data;

    if (!superiorAccount.trim()) {
      wx.showToast({
        title: '请输入上级用户账号',
        icon: 'none'
      });
      return;
    }

    if (superiorAccount === userInfo.account) {
      wx.showToast({
        title: '不能绑定自己为上级',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '验证用户中...'
    });

    // 首先验证上级用户是否存在
    this.checkAccountExists(superiorAccount.trim())
      .then((accountData) => {
        // 用户存在，继续绑定流程
        console.log('用户验证成功，开始绑定流程');
        wx.showLoading({
          title: '绑定中...'
        });

        // 调用绑定上级用户API
        wx.request({
          url: `${app.globalData.apiConfig.baseUrl}/api/user-relation`,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          data: {
            account: userInfo.account,
            parentAccount: superiorAccount.trim()
          },
          success: (res) => {
            wx.hideLoading();
            console.log('绑定上级用户响应:', res);

            if (res.data.code === 200) {
              wx.showToast({
                title: '绑定成功',
                icon: 'success'
              });

              // 重新获取上级用户信息
              setTimeout(() => {
                this.getSuperiorUser();
                this.setData({
                  superiorAccount: ''
                });
              }, 1500);
            } else {
              wx.showToast({
                title: res.data.message || '绑定失败',
                icon: 'none'
              });
            }
          },
          fail: (error) => {
            wx.hideLoading();
            console.error('绑定上级用户失败:', error);
            wx.showToast({
              title: '网络错误，请稍后重试',
              icon: 'none'
            });
          }
        });
      })
      .catch((error) => {
        // 用户不存在或其他错误
        console.log('用户验证失败:', error.message);
        wx.hideLoading();

        // 确保加载提示被隐藏
        setTimeout(() => {
          wx.hideLoading();
        }, 100);

        wx.showToast({
          title: error.message || '该用户不存在',
          icon: 'none',
          duration: 2000
        });
      });
  },



  // 加载邀请列表
  loadInviteList: function (refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        inviteList: []
      });
    }

    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }

    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.account) {
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return new Promise((resolve) => {
      // 预留API接口：获取邀请列表
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/user/invite-list/${userInfo.account}`,
        method: 'GET',
        data: {
          page: this.data.page,
          pageSize: this.data.pageSize
        },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('获取邀请列表响应:', res);
          this.setData({ loading: false });

          if (res.data.code === 200 && res.data.data) {
            const responseData = res.data.data;
            const newInvites = (responseData.records || []).map(user => {
              return {
                id: user.id,
                account: user.account,
                username: user.username,
                avatar: user.avatar,
                createTime: this.formatTime(user.createTime)
              };
            });

            this.setData({
              inviteList: this.data.inviteList.concat(newInvites),
              inviteCount: responseData.total || 0,
              hasMore: responseData.current < responseData.pages,
              page: this.data.page + 1
            });
          }
          resolve();
        },
        fail: (error) => {
          console.error('获取邀请列表失败:', error);
          this.setData({ loading: false });
          resolve();
        }
      });
    });
  },



  // 跳转到佣金页面
  goToCommission: function () {
    wx.navigateTo({
      url: '/pages/commission/index'
    });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    return `${year}年${month}月${day}日`;
  }
})
