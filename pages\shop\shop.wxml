<!--pages/shop/shop.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <icon type="search" size="16" color="#999"></icon>
      <input class="search-input" placeholder="儿童" confirm-type="search" value="{{keyword}}" bindconfirm="searchProducts"></input>
    </view>
    <view class="search-button" bindtap="searchProducts">搜索</view>
    <view class="cart-button">
      <image class="cart-icon" src="/images/icons/icon-cart.png"></image>
    </view>
  </view>

  <!-- 促销横幅 -->
  <view class="promo-banner">
    <view class="promo-text">
      <text class="promo-title">0服务费金条来袭！</text>
      <text class="promo-subtitle">金价<text class="promo-highlight">+2</text>可得</text>
    </view>
    <view class="promo-image">
      <image class="gold-bar-image" src="/images/gold-bar.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 分类网格 -->
  <view class="category-grid">
    <view class="category-row">
      <view class="category-item" bindtap="changeCategory" data-category="ring">
        <image class="category-icon" src="" mode="aspectFit"></image>
        <text class="category-name">手镯</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="necklace">
        <image class="category-icon" src="/images/categories/necklace.png" mode="aspectFit"></image>
        <text class="category-name">项链</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="bracelet">
        <image class="category-icon" src="/images/categories/bracelet.png" mode="aspectFit"></image>
        <text class="category-name">戒指</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="pendant">
        <image class="category-icon" src="/images/categories/pendant.png" mode="aspectFit"></image>
        <text class="category-name">吊坠</text>
      </view>
    </view>
    <view class="category-row">
      <view class="category-item" bindtap="changeCategory" data-category="bangle">
        <image class="category-icon" src="/images/categories/bangle.png" mode="aspectFit"></image>
        <text class="category-name">手链</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="earring">
        <image class="category-icon" src="/images/categories/earring.jpg" mode="aspectFit"></image>
        <text class="category-name">耳饰</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="goldbar">
        <image class="category-icon" src="/images/categories/goldbar.jpg" mode="aspectFit"></image>
        <text class="category-name">手串</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="goldbar">
        <image class="category-icon" src="/images/categories/5D.png" mode="aspectFit"></image>
        <text class="category-name">5D</text>
      </view>
    </view>
    <view class="category-row">
      <view class="category-item" bindtap="changeCategory" data-category="bangle">
        <image class="category-icon" src="/images/categories/oldgold.png" mode="aspectFit"></image>
        <text class="category-name">古法金</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="earring">
        <image class="category-icon" src="/images/categories/gold.png" mode="aspectFit"></image>
        <text class="category-name">黄金</text>
      </view>
      <view class="category-item" bindtap="changeCategory" data-category="goldbar">
        <image class="category-icon" src="/images/categories/3D.png" mode="aspectFit"></image>
        <text class="category-name">3D</text>
      </view>
      <view class="category-item find-more" bindtap="showAllCategories">
        <view class="find-more-content">
          <text class="find-more-text">其他 ></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热销商品 -->
  <view class="hot-products-section">
    <view class="section-header">
      <view class="section-title-wrap">
        <image class="hot-icon" src="/images/icons/hot.png"></image>
        <text class="section-title">热销商品</text>
      </view>
      <view class="section-more" bindtap="showMoreHotProducts">更多</view>
    </view>

    <view class="hot-products-grid">
      <view class="hot-product-item" wx:for="{{hotProducts}}" wx:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
        <view class="hot-product-tag">{{item.tag}}</view>
        <image class="hot-product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="hot-product-info">
          <view class="hot-product-price">¥{{item.price}}/克</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 精选商品 -->
  <view class="featured-products-section">
    <view class="section-header">
      <view class="section-title-wrap">
        <image class="featured-icon" src="/images/icons/featured.png"></image>
        <text class="section-title">精选商品</text>
      </view>
      <view class="section-more" bindtap="showMoreFeaturedProducts">更多</view>
    </view>

    <view class="featured-products-list">
      <view class="featured-product-item" wx:for="{{featuredProducts}}" wx:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
        <image class="featured-product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="featured-product-info">
          <view class="featured-product-name">{{item.name}}</view>
          <view class="featured-product-price-range">
            <text class="price-range">{{item.priceRange}}</text>
            <text class="product-tag">{{item.tag}}</text>
          </view>
          <view class="featured-product-bottom">
            <view class="featured-product-tag">{{item.productTag}}</view>
            <view class="featured-product-price">工费¥{{item.price}}/克</view>
          </view>
          <view class="featured-product-sales">{{item.minOrder}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-container" wx:if="{{products.length > 0}}">
    <view class="products-list">
      <view
        class="product-card"
        wx:for="{{products}}"
        wx:key="_id"
        bindtap="goToProductDetail"
        data-id="{{item._id}}"
      >
        <image class="product-image" src="{{item.images[0]}}" mode="aspectFill"></image>
        <view class="product-info">
          <view class="product-name ellipsis">{{item.name}}</view>
          <view class="product-meta">
            <text class="product-weight">{{item.goldWeight}}克</text>
            <text class="product-purity">{{item.purity}}</text>
          </view>
          <view class="product-price-row">
            <view class="product-price">¥{{item.price/100}}</view>
            <view class="product-sales">已售{{item.sales}}件</view>
          </view>
        </view>
        <view class="product-tags">
          <view class="product-tag hot" wx:if="{{item.isHot}}">热门</view>
          <view class="product-tag new" wx:if="{{item.isNew}}">新品</view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container">
      <view class="loading" wx:if="{{loading}}">正在加载...</view>
      <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">没有更多商品了</view>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-mask" bindtap="hideFilterPanel"></view>
    <view class="filter-content">
      <view class="filter-header">
        <view class="filter-title">排序方式</view>
        <view class="filter-close" bindtap="hideFilterPanel">×</view>
      </view>
      <view class="sort-options">
        <view
          class="sort-option {{currentSort === item.id ? 'active' : ''}}"
          wx:for="{{sortOptions}}"
          wx:key="id"
          bindtap="changeSort"
          data-sort="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
  </view>
</view>
