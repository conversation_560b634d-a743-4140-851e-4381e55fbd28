// pages/order/detail.js
const app = getApp();

Page({
  data: {
    orderId: '',
    order: null,
    addressInfo: null, // 地址详情信息
    expressInfo: null, // 物流信息
    quotationInfo: null, // 检测结果详情信息
    loading: true,
    loadingAddress: false, // 地址加载状态
    loadingExpress: false, // 物流加载状态
    loadingQuotation: false, // 检测结果加载状态
    showCancelModal: false,
    cancelReason: '',
    cancelReasons: [
      '不想回收了',
      '信息填写错误',
      '价格不满意',
      '其他原因'
    ],
    selectedReason: '',
    currentImageIndex: 0 // 当前查看的图片索引
  },

  onLoad: function (options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadOrderDetail();
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadOrderDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onShareAppMessage: function () {
    return {
      title: '黄金回收订单详情',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 加载订单详情
  loadOrderDetail: function () {
    this.setData({ loading: true });

    return new Promise((resolve) => {
      // 检查用户登录状态
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.isLogin) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        this.setData({ loading: false });
        resolve();
        return;
      }

      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${this.data.orderId}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('订单详情API响应:', res);
          this.setData({ loading: false });

          if (res.data.code === 200 && res.data.data) {
            const order = this.processOrderData(res.data.data);
            this.setData({
              order: order
            });

            // 如果订单有地址ID，获取地址详情
            if (order.addressId) {
              this.loadAddressDetail(order.addressId);
            }

            // 如果订单状态为待取件，获取快递信息
            if (order.status === 2) {
              this.loadPickupInfo(order.orderId);
            }

            // 如果订单状态为已检测、已确认或已完成，获取检测结果详情
            if (order.status === 4 || order.status === 5 || order.status === 6) {
              this.loadQuotationInfo(order.orderId);
            }
          } else {
            wx.showToast({
              title: res.data.message || '获取订单详情失败',
              icon: 'none'
            });
          }
          resolve();
        },
        fail: (error) => {
          console.error('获取订单详情失败:', error);
          this.setData({ loading: false });
          wx.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
          resolve();
        }
      });
    });
  },

  // 处理订单数据
  processOrderData: function (order) {
    // 处理图片列表
    let imageList = [];

    // 优先使用 imageList 字段
    if (order.imageList && Array.isArray(order.imageList)) {
      imageList = order.imageList;
    }
    // 如果 imageList 不存在，尝试解析 imageBase64 字段
    else if (order.imageBase64) {
      try {
        // imageBase64 可能是字符串化的JSON数组
        if (typeof order.imageBase64 === 'string') {
          // 尝试解析JSON字符串
          const parsed = JSON.parse(order.imageBase64);
          if (Array.isArray(parsed)) {
            imageList = parsed;
          } else {
            // 如果不是数组，可能是单个URL字符串
            imageList = [order.imageBase64];
          }
        } else if (Array.isArray(order.imageBase64)) {
          imageList = order.imageBase64;
        }
      } catch (error) {
        console.error('解析图片数据失败:', error, order.imageBase64);
        // 解析失败时，尝试直接使用原始数据
        if (order.imageBase64) {
          imageList = [order.imageBase64];
        }
      }
    }

    // 过滤掉无效的图片URL
    imageList = imageList.filter(url => url && typeof url === 'string' && url.trim() !== '');

    console.log('处理订单详情图片:', {
      orderId: order.orderId,
      originalImageBase64: order.imageBase64,
      originalImageList: order.imageList,
      processedImageList: imageList
    });

    return {
      id: order.id,
      orderId: order.orderId,
      account: order.account,
      goldType: order.goldType,
      goldTypeText: this.getGoldTypeText(order.goldType),
      goldCondition: order.goldCondition,
      purity: order.purity,
      estimatedWeight: this.formatWeight(order.estimatedWeight),
      estimatedPrice: this.formatPrice(order.estimatedPrice),
      finalPrice: this.formatPrice(order.finalPrice),
      status: order.status,
      statusText: this.getStatusText(order.status),
      description: order.description,
      inspectionResult: order.inspectionResult,
      receiverName: order.receiverName,
      receiverPhone: order.receiverPhone,
      receiverAddress: order.receiverAddress,
      addressId: order.addressId, // 地址ID
      expressNumber: order.expressNumber, // 快递单号
      expressCompany: order.expressCompany, // 快递公司编码
      imageBase64: order.imageBase64,
      imageList: imageList, // 处理后的图片列表
      hasImage: imageList.length > 0, // 是否有图片
      imageCount: imageList.length, // 图片数量
      createTime: order.createTime,
      updateTime: order.updateTime,
      createTimeFormatted: this.formatTime(order.createTime),
      updateTimeFormatted: this.formatTime(order.updateTime),
      rawData: order
    };
  },

  // 获取状态文字
  getStatusText: function (status) {
    const statusMap = {
      0: '已取消',
      1: '已下单',
      2: '待取件',
      3: '待检测',
      4: '检测完成',
      5: '已确认',
      6: '订单已完成'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取黄金类型文字
  getGoldTypeText: function (goldType) {
    const typeMap = {
      'jewelry': '黄金',
      'broken': '钯金',
      'bar': '铂金',
      'other': '其他'
    };
    return typeMap[goldType] || goldType || '未知类型';
  },

  // 格式化价格
  formatPrice: function (price) {
    if (price === null || price === undefined) return '0.00';
    return Number(price).toFixed(2);
  },

  // 格式化重量
  formatWeight: function (weight) {
    if (weight === null || weight === undefined) return '0.00';
    return Number(weight).toFixed(2);
  },

  // 获取地址详情
  loadAddressDetail: function(addressId) {
    if (!addressId) {
      console.log('没有地址ID，跳过地址详情获取');
      return;
    }

    this.setData({ loadingAddress: true });

    // 构建查询参数
    const queryParams = `addressId=${encodeURIComponent(addressId)}`;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/user/address/detail?${queryParams}`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('地址详情API响应:', res);
        this.setData({ loadingAddress: false });

        if (res.data.code === 200 && res.data.data) {
          const addressInfo = this.processAddressData(res.data.data);
          this.setData({
            addressInfo: addressInfo
          });
        } else {
          console.error('获取地址详情失败:', res.data.message);
          // 地址获取失败不影响主要功能，只记录错误
        }
      },
      fail: (error) => {
        console.error('获取地址详情请求失败:', error);
        this.setData({ loadingAddress: false });
        // 地址获取失败不影响主要功能，只记录错误
      }
    });
  },

  // 处理地址数据
  processAddressData: function(addressData) {
    return {
      addressId: addressData.addressId,
      account: addressData.account,
      receiverName: addressData.receiverName,
      receiverPhone: addressData.receiverPhone,
      province: addressData.province,
      city: addressData.city,
      district: addressData.district,
      detailAddress: addressData.detailAddress,
      postCode: addressData.postCode,
      isDefault: addressData.isDefault,
      // 组合完整地址
      fullAddress: `${addressData.province || ''}${addressData.city || ''}${addressData.district || ''}${addressData.detailAddress || ''}`.trim()
    };
  },

  // 获取检测结果详情
  loadQuotationInfo: function(orderId) {
    if (!orderId) {
      console.log('没有订单ID，跳过检测结果获取');
      return;
    }

    this.setData({ loadingQuotation: true });

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/quotations/order/${orderId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('检测结果详情API响应:', res);
        this.setData({ loadingQuotation: false });

        if (res.data.code === 200 && res.data.data) {
          const quotationInfo = this.processQuotationData(res.data.data);
          this.setData({
            quotationInfo: quotationInfo
          });
        } else {
          console.error('获取检测结果详情失败:', res.data.message);
          // 检测结果获取失败不影响主要功能，只记录错误
        }
      },
      fail: (error) => {
        console.error('获取检测结果详情请求失败:', error);
        this.setData({ loadingQuotation: false });
        // 检测结果获取失败不影响主要功能，只记录错误
      }
    });
  },

  // 处理检测结果数据
  processQuotationData: function(quotationDataArray) {
    // 根据最新API文档，返回的是报价记录数组
    if (!quotationDataArray || !Array.isArray(quotationDataArray) || quotationDataArray.length === 0) {
      return {
        quotations: [],
        hasQuotation: false,
        totalAmount: '0.00',
        totalFees: '0.00',
        finalAmount: '0.00',
        otherItems: '0.00'
      };
    }

    // 处理所有报价记录
    const processedQuotations = quotationDataArray.map(quotation => {
      // 处理每个报价记录的检测项
      const processedItems = (quotation.items || []).map(item => {
        const price = parseFloat(item.price) || 0;
        const pureGoldWeight = parseFloat(item.pureGoldWeight) || 0;
        const itemTotal = price * pureGoldWeight; // 价格 * 纯金重

        return {
          id: item.id,
          goldType: item.goldType,
          goldTypeText: this.getGoldTypeText(item.goldType),
          price: this.formatPrice(price),
          weight: this.formatWeight(item.weight),
          meltedWeight: this.formatWeight(item.meltedWeight),
          pureGoldWeight: this.formatWeight(pureGoldWeight),
          goldContent: item.goldContent ? `${item.goldContent}%` : '0%',
          imageUrl: item.imageUrl || '',
          itemTotal: this.formatPrice(itemTotal) // 单项总价 = 价格 * 纯金重
        };
      });

      // 计算当前报价记录的总价（所有检测项的 价格*纯金重 之和）
      const quotationTotal = processedItems.reduce((sum, item) => {
        return sum + (parseFloat(item.itemTotal) || 0);
      }, 0);

      return {
        id: quotation.id,
        orderId: quotation.orderId,
        createTime: quotation.createTime,
        updateTime: quotation.updateTime,
        createTimeFormatted: this.formatTime(quotation.createTime),
        updateTimeFormatted: this.formatTime(quotation.updateTime),
        items: processedItems,
        itemCount: processedItems.length,
        shippingFee: this.formatPrice(quotation.shippingFee),
        insuranceFee: this.formatPrice(quotation.insuranceFee),
        serviceFee: this.formatPrice(quotation.serviceFee),
        otherItems: this.formatPrice(quotation.otherItems),
        quotationTotal: this.formatPrice(quotationTotal),
        quotationFinalAmount: this.formatPrice(quotationTotal - (quotation.shippingFee || 0) - (quotation.insuranceFee || 0) - (quotation.serviceFee || 0))
      };
    });

    // 计算所有报价的汇总信息
    let totalAmount = 0;
    let totalShippingFee = 0;
    let totalInsuranceFee = 0;
    let totalServiceFee = 0;
    let totalOtherItems = 0;

    processedQuotations.forEach(quotation => {
      totalAmount += parseFloat(quotation.quotationTotal) || 0;
      totalShippingFee += parseFloat(quotation.shippingFee) || 0;
      totalInsuranceFee += parseFloat(quotation.insuranceFee) || 0;
      totalServiceFee += parseFloat(quotation.serviceFee) || 0;
      totalOtherItems += parseFloat(quotation.otherItems) || 0;
    });

    const totalFees = totalShippingFee + totalInsuranceFee + totalServiceFee + totalOtherItems;
    const finalAmount = totalAmount - totalFees;

    return {
      quotations: processedQuotations,
      quotationCount: processedQuotations.length,
      hasQuotation: processedQuotations.length > 0,
      totalAmount: this.formatPrice(totalAmount),
      totalShippingFee: this.formatPrice(totalShippingFee),
      totalInsuranceFee: this.formatPrice(totalInsuranceFee),
      totalServiceFee: this.formatPrice(totalServiceFee),
      otherItems: this.formatPrice(totalOtherItems),
      totalFees: this.formatPrice(totalFees),
      finalAmount: this.formatPrice(finalAmount)
    };
  },

  // 显示取消订单面板
  showCancel: function () {
    this.setData({
      showCancelModal: true,
      selectedReason: this.data.cancelReasons[0],
      cancelReason: this.data.cancelReasons[0]
    });
  },

  // 隐藏取消订单面板
  hideCancel: function () {
    this.setData({
      showCancelModal: false
    });
  },

  // 选择取消原因
  selectReason: function (e) {
    const reason = e.currentTarget.dataset.reason;
    this.setData({
      selectedReason: reason,
      cancelReason: reason
    });
  },

  // 输入其他取消原因
  inputCancelReason: function (e) {
    this.setData({
      cancelReason: e.detail.value
    });
  },

  // 确认取消订单
  confirmCancel: function () {
    if (!this.data.order || !this.data.cancelReason) {
      wx.showToast({
        title: '请选择或输入取消原因',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    // 调用取消订单API
    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${this.data.orderId}/cancel`,
      method: 'PUT',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('取消订单API响应:', res);

        if (res.data.code === 200) {
          // 更新本地数据
          const order = this.data.order;
          order.status = 0; // 0 = 已取消
          order.statusText = '已取消';
          order.cancelReason = this.data.cancelReason;

          this.setData({
            order: order,
            showCancelModal: false
          });

          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.message || '取消失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('取消订单失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage: function (e) {
    if (this.data.order && this.data.order.hasImage) {
      const currentIndex = e.currentTarget.dataset.index || 0;
      const currentUrl = this.data.order.imageList[currentIndex];

      wx.previewImage({
        current: currentUrl,
        urls: this.data.order.imageList
      });
    }
  },

  // 预览检测结果图片
  previewQuotationImage: function (e) {
    const imageUrl = e.currentTarget.dataset.url;
    if (imageUrl) {
      // 收集所有检测结果图片
      const allImages = [];
      if (this.data.quotationInfo && this.data.quotationInfo.quotations) {
        this.data.quotationInfo.quotations.forEach(quotation => {
          if (quotation.items) {
            quotation.items.forEach(item => {
              if (item.imageUrl) {
                allImages.push(item.imageUrl);
              }
            });
          }
        });
      }

      wx.previewImage({
        current: imageUrl,
        urls: allImages.length > 0 ? allImages : [imageUrl]
      });
    }
  },

  // 图片加载成功
  onImageLoad: function(e) {
    console.log('图片加载成功');
  },

  // 图片加载失败
  onImageError: function(e) {
    console.error('图片加载失败:', e.detail);
    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 1000
    });
  },

  // 复制订单号
  copyOrderId: function () {
    wx.setClipboardData({
      data: this.data.orderId,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 联系客服
  contactService: function () {
    // 获取当前订单号
    const orderId = this.data.orderId || '';

    // 跳转到客服页面
    const url = orderId
      ? `/pages/customerService/index?orderId=${orderId}`
      : '/pages/customerService/index';

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('跳转客服页面失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取订单快递信息
  loadPickupInfo: function(orderId) {
    if (!orderId) {
      console.log('没有订单ID，跳过快递信息获取');
      return;
    }

    this.setData({ loadingExpress: true });

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${orderId}/pickup`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('快递信息API响应:', res);
        this.setData({ loadingExpress: false });

        if (res.data.code === 200 && res.data.data) {
          const pickupData = res.data.data;

          // 如果有快递单号，调用物流轨迹API
          if (pickupData.expressNumber || pickupData.trackingNumber) {
            const expressNumber = pickupData.expressNumber || pickupData.trackingNumber;
            const expressCompany = pickupData.expressCompany || pickupData.courierCompany;

            // 保存快递基本信息
            this.setData({
              pickupInfo: {
                expressNumber: expressNumber,
                expressCompany: expressCompany,
                courierName: pickupData.courierName || '',
                courierPhone: pickupData.courierPhone || '',
                pickupTime: pickupData.pickupTime || '',
                estimatedDelivery: pickupData.estimatedDelivery || ''
              }
            });

            // 获取详细物流轨迹
            this.loadExpressInfo(expressNumber, expressCompany);
          } else {
            // 没有快递单号，只显示基本信息
            this.setData({
              expressInfo: {
                error: true,
                message: '暂无快递单号信息'
              }
            });
          }
        } else {
          console.error('获取快递信息失败:', res.data);
          this.setData({
            expressInfo: {
              error: true,
              message: res.data.message || '获取快递信息失败'
            }
          });
        }
      },
      fail: (error) => {
        console.error('获取快递信息请求失败:', error);
        this.setData({
          loadingExpress: false,
          expressInfo: {
            error: true,
            message: '网络错误，请重试'
          }
        });
      }
    });
  },

  // 加载物流信息
  loadExpressInfo: function(expressNumber, expressCompany) {
    if (!expressNumber) {
      console.log('没有快递单号，跳过物流查询');
      return;
    }

    this.setData({ loadingExpress: true });

    // 构建请求参数
    const params = {
      key: app.globalData.apiConfig.expressApiKey, // 从全局配置获取API密钥
      no: expressNumber
    };

    // 如果有快递公司编码，添加到参数中
    if (expressCompany) {
      params.com = expressCompany;
    }

    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    wx.request({
      url: `https://api.tanshuapi.com/api/exp/v1/index?${queryString}`,
      method: 'GET',
      success: (res) => {
        console.log('物流信息API响应:', res);
        this.setData({ loadingExpress: false });

        if (res.data && (res.data.code === 1 || res.data.code === 0) && res.data.data) {
          const expressInfo = this.processExpressData(res.data.data);
          this.setData({
            expressInfo: expressInfo
          });
        } else {
          console.error('获取物流信息失败:', res.data);
          // 物流信息获取失败不影响主要功能
          this.setData({
            expressInfo: {
              error: true,
              message: res.data.msg || '暂无物流信息'
            }
          });
        }
      },
      fail: (error) => {
        console.error('获取物流信息请求失败:', error);
        this.setData({
          loadingExpress: false,
          expressInfo: {
            error: true,
            message: '获取物流信息失败'
          }
        });
      }
    });
  },

  // 处理物流数据
  processExpressData: function(data) {
    // 处理物流轨迹数据
    let tracks = [];
    if (data.list && Array.isArray(data.list)) {
      tracks = data.list.map(item => ({
        time: item.datetime,
        status: item.remark,
        location: '' // API响应中没有单独的location字段，信息都在remark中
      }));
    }

    return {
      company: data.company || '未知快递公司',
      status: this.getExpressStatusText(data.status_detail),
      statusDetail: data.status_detail,
      statusDesc: data.status_desc || '',
      takeTime: data.take_time || '',
      comPhone: data.com_phone || '',
      comUrl: data.com_url || '',
      courierPhone: data.courier_phone || '',
      expressNumber: data.no || '',
      tracksCount: data.tracks_count || 0,
      tracks: tracks,
      hasTracking: tracks.length > 0,
      error: false
    };
  },

  // 获取物流状态文字
  getExpressStatusText: function(statusDetail) {
    const statusMap = {
      '1': '已揽件',
      '2': '运输中',
      '3': '派送中',
      '4': '已签收',
      '5': '包裹异常',
      '10': '退回'
    };
    return statusMap[statusDetail] || '未知状态';
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 格式化价格
  formatPrice: function(price) {
    if (price === null || price === undefined) return '0.00';
    return parseFloat(price).toFixed(2);
  },

  // 格式化重量
  formatWeight: function(weight) {
    if (weight === null || weight === undefined) return '0.00';
    return parseFloat(weight).toFixed(2);
  },

  // 获取黄金类型显示文本（与列表页面保持一致）
  getGoldTypeText: function(goldType) {
    // 首先检查是否是订单的基础类型
    const baseTypeMap = {
      'jewelry': '黄金',
      'broken': '钯金',
      'bar': '铂金',
      'other': '其他'
    };

    // 如果是基础类型，返回对应的文本
    if (baseTypeMap[goldType]) {
      return baseTypeMap[goldType];
    }

    // 如果是具体的金属类型，返回详细描述
    const detailTypeMap = {
      'Au9999': 'Au9999 (万足金)',
      'Au999': 'Au999 (千足金)',
      '足金': '足金',
      'Au916': 'Au916 (22K金)',
      'Au750': 'Au750 (18K金)',
      'Pt999': 'Pt999 (千足铂)',
      'Pt990': 'Pt990 (足铂)',
      'Pt950': 'Pt950 (铂金)',
      'Pd999': 'Pd999 (千足钯)',
      'Pd990': 'Pd990 (足钯)',
      'Pd950': 'Pd950 (钯金)'
    };

    return detailTypeMap[goldType] || goldType || '未知类型';
  },

  // 复制快递单号
  copyExpressNumber: function () {
    const expressNumber = this.data.pickupInfo?.expressNumber || this.data.order?.expressNumber;
    if (expressNumber) {
      wx.setClipboardData({
        data: expressNumber,
        success: () => {
          wx.showToast({
            title: '快递单号已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 拨打取件快递员电话
  callPickupCourier: function () {
    if (this.data.pickupInfo?.courierPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.pickupInfo.courierPhone,
        fail: () => {
          wx.showToast({
            title: '拨打失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 拨打快递员电话
  callCourier: function () {
    if (this.data.expressInfo.courierPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.expressInfo.courierPhone,
        fail: () => {
          wx.showToast({
            title: '拨打失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 拨打快递公司客服电话
  callCompany: function () {
    if (this.data.expressInfo.comPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.expressInfo.comPhone,
        fail: () => {
          wx.showToast({
            title: '拨打失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 重新获取物流信息
  retryLoadExpress: function () {
    if (this.data.order && this.data.order.status === 2) {
      // 重新获取快递信息
      this.loadPickupInfo(this.data.order.orderId);
    }
  },

  // 确认最终价格
  confirmFinalPrice: function () {
    if (!this.data.order || this.data.order.status !== 4) {
      wx.showToast({
        title: '订单状态异常',
        icon: 'none'
      });
      return;
    }

    // 获取检测结果的最终金额
    let finalAmount = '0.00';
    if (this.data.quotationInfo && this.data.quotationInfo.hasQuotation) {
      finalAmount = this.data.quotationInfo.finalAmount;
    } else if (this.data.order.finalPrice && this.data.order.finalPrice !== '0.00') {
      finalAmount = this.data.order.finalPrice;
    }

    wx.showModal({
      title: '确认最终价格',
      content: `确认接受最终价格 ¥${finalAmount} 吗？`,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.updateOrderStatus(5); // 更新状态为已确认
        }
      }
    });
  },

  // 拒绝最终价格
  rejectFinalPrice: function () {
    wx.showModal({
      title: '价格异议',
      content: '如对检测结果有异议，请联系客服处理',
      showCancel: false,
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          this.contactService();
        }
      }
    });
  },

  // 更新订单状态
  updateOrderStatus: function (newStatus) {
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${this.data.orderId}/status?status=${newStatus}`,
      method: 'PUT',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('更新订单状态API响应:', res);

        if (res.data.code === 200) {
          // 更新本地数据
          const order = this.data.order;
          order.status = newStatus;
          order.statusText = this.getStatusText(newStatus);

          this.setData({
            order: order
          });

          wx.showToast({
            title: newStatus === 5 ? '已确认最终价格' : '状态更新成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.message || '更新失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('更新订单状态失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
})
