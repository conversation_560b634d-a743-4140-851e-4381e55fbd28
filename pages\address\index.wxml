<!--pages/address/index.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <view class="address-item" wx:for="{{addressList}}" wx:key="id">
      <view class="address-info" bindtap="selectAddress" data-id="{{item.addressId}}">
        <view class="address-header">
          <view class="address-name-phone">
            <text class="address-name">{{item.receiverName}}</text>
            <text class="address-phone">{{item.receiverPhone}}</text>
          </view>
          <view class="address-tag" wx:if="{{item.isDefault}}">默认</view>
        </view>
        <view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.detailAddress}}</view>
      </view>
      <view class="address-actions">
        <view class="action-item" bindtap="setDefault" data-id="{{item.addressId}}" wx:if="{{!item.isDefault}}">
          <image class="action-icon" src="/images/icons/icon-default.png"></image>
          <text class="action-text">设为默认</text>
        </view>
        <view class="action-item" bindtap="editAddress" data-id="{{item.addressId}}">
          <image class="action-icon" src="/images/icons/icon-edit.png"></image>
          <text class="action-text">编辑</text>
        </view>
        <view class="action-item" bindtap="deleteAddress" data-id="{{item.addressId}}">
          <image class="action-icon" src="/images/icons/icon-delete.png"></image>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:else>
    <text class="empty-text">暂无收货地址</text>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address-btn" bindtap="showAddressForm">
    <text class="btn-icon">+</text>
    <text class="btn-text">新增收货地址</text>
  </view>

  <!-- 地址表单弹窗 -->
  <view class="address-modal {{showForm ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideAddressForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">{{editingAddress ? '编辑地址' : '新增地址'}}</view>
        <view class="modal-close" bindtap="hideAddressForm">×</view>
      </view>
      <view class="modal-body">
        <form bindsubmit="saveAddress">
          <view class="form-item">
            <view class="form-label required">收货人</view>
            <input class="form-input" name="name" placeholder="请输入收货人姓名" value="{{formData.name}}" />
          </view>
          <view class="form-item">
            <view class="form-label required">手机号码</view>
            <input class="form-input" name="phone" type="number" maxlength="11" placeholder="请输入手机号码" value="{{formData.phone}}" />
          </view>
          <view class="form-item">
            <view class="form-label required">所在地区</view>
            <picker mode="region" bindchange="regionChange" value="{{region}}">
              <view class="region-picker {{region[0] ? '' : 'placeholder'}}">
                {{region[0] ? region[0] + ' ' + region[1] + ' ' + region[2] : '请选择所在地区'}}
              </view>
            </picker>
          </view>
          <view class="form-item">
            <view class="form-label required">详细地址</view>
            <textarea class="form-textarea" name="detail" placeholder="请输入详细地址，如街道、门牌号等" value="{{formData.detail}}"></textarea>
          </view>
          <view class="form-item switch-item">
            <view class="switch-label">设为默认地址</view>
            <switch name="isDefault" color="#4A1010" checked="{{formData.isDefault}}" />
          </view>
          <button class="submit-btn" form-type="submit">保存</button>
        </form>
      </view>
    </view>
  </view>
</view>
