import { ElMessage } from 'element-plus'
import { getUserRelationFee, getUserDetails, getPlatformFeeRate, depositToAccount } from '@/api/rebate'

/**
 * 处理返点分佣
 * @param {string} account - 当前用户账号
 * @param {number} weight - 订单重量
 * @returns {Promise<boolean>} 返回处理结果
 */
export async function processRebate(account, weight) {
  try {
    console.log(`开始处理返点分佣 - 用户: ${account}, 重量: ${weight}`)
    
    // 参数验证
    if (!account || !weight || weight <= 0) {
      console.warn('返点分佣参数无效:', { account, weight })
      return false
    }

    // 1. 获取用户关系费率信息
    let relationFeeData
    try {
      const relationFeeResponse = await getUserRelationFee(account)
      relationFeeData = relationFeeResponse.data
      console.log('用户关系费率信息:', relationFeeData)
    } catch (error) {
      console.error('获取用户关系费率信息失败:', error)
      ElMessage.warning('获取用户关系信息失败，跳过返点处理')
      return false
    }

    // 检查是否有上级用户
    if (!relationFeeData.parentId) {
      console.log('用户无上级，无需返点')
      return true
    }

    // 2. 获取当前用户详情
    let currentUserData
    try {
      const userResponse = await getUserDetails(account)
      currentUserData = userResponse.data
      console.log('当前用户详情:', currentUserData)
    } catch (error) {
      console.error('获取当前用户详情失败:', error)
      ElMessage.error('获取用户详情失败')
      return false
    }

    // 3. 获取当前用户的手续费率
    let currentUserFeeRate = 0
    try {
      const feeResponse = await getPlatformFeeRate(currentUserData.userLevel)
      currentUserFeeRate = feeResponse.data.feeRate || 0
      console.log('当前用户手续费率:', currentUserFeeRate)
    } catch (error) {
      console.error('获取当前用户手续费率失败:', error)
      ElMessage.error('获取手续费率失败')
      return false
    }

    // 4. 处理上级用户返点
    if (relationFeeData.parentId && relationFeeData.parentFeeRate !== null && relationFeeData.parentFeeRate !== undefined) {
      const parentRebateAmount = (currentUserFeeRate - relationFeeData.parentFeeRate) * weight
      console.log(`上级用户返点计算: (${currentUserFeeRate} - ${relationFeeData.parentFeeRate}) * ${weight} = ${parentRebateAmount}`)
      
      if (parentRebateAmount > 0) {
        try {
          await depositToAccount(relationFeeData.parentId, {
            amount: parentRebateAmount,
            transactionId: Date.now().toString(),
            remark: `${account}返点`
          })
          console.log(`上级用户 ${relationFeeData.parentId} 返点成功: ${parentRebateAmount}`)
        } catch (error) {
          console.error('上级用户返点失败:', error)
          ElMessage.error(`上级用户返点失败: ${error.message}`)
        }
      } else {
        console.log('上级用户返点金额为负数或零，跳过返点')
      }
    }

    // 5. 处理上上级用户返点
    if (relationFeeData.grandParentId && relationFeeData.grandParentFeeRate !== null && relationFeeData.grandParentFeeRate !== undefined) {
      const grandParentRebateAmount = (currentUserFeeRate - relationFeeData.parentFeeRate - relationFeeData.grandParentFeeRate) * weight
      console.log(`上上级用户返点计算: (${currentUserFeeRate} - ${relationFeeData.parentFeeRate} - ${relationFeeData.grandParentFeeRate}) * ${weight} = ${grandParentRebateAmount}`)
      
      if (grandParentRebateAmount > 0) {
        try {
          await depositToAccount(relationFeeData.grandParentId, {
            amount: grandParentRebateAmount,
            transactionId: Date.now().toString() + '_gp', // 添加后缀避免重复
            remark: `${account}返点`
          })
          console.log(`上上级用户 ${relationFeeData.grandParentId} 返点成功: ${grandParentRebateAmount}`)
        } catch (error) {
          console.error('上上级用户返点失败:', error)
          ElMessage.error(`上上级用户返点失败: ${error.message}`)
        }
      } else {
        console.log('上上级用户返点金额为负数或零，跳过返点')
      }
    }

    console.log('返点分佣处理完成')
    return true

  } catch (error) {
    console.error('返点分佣处理异常:', error)
    ElMessage.error(`返点处理失败: ${error.message}`)
    return false
  }
}

/**
 * 带加载状态的返点处理函数
 * @param {string} account - 当前用户账号
 * @param {number} weight - 订单重量
 * @param {Function} setLoading - 设置加载状态的函数
 * @returns {Promise<boolean>} 返回处理结果
 */
export async function processRebateWithLoading(account, weight, setLoading) {
  if (setLoading) setLoading(true)
  
  try {
    const result = await processRebate(account, weight)
    return result
  } finally {
    if (setLoading) setLoading(false)
  }
}
