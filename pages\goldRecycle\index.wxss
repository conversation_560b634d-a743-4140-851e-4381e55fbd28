/* pages/goldRecycle/index.wxss */
.container {
  padding-bottom: 40rpx;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 60rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number-1 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.step-text {
  font-size: 24rpx;
  color: #999;
}

.step-line {
  flex: 1;
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 0 10rpx;
  margin-bottom: 30rpx;
}

.step-item.active .step-number-1 {
  background-color: var(--primary-color);
  color: #fff;
}

.step-item.active .step-text {
  color: var(--primary-color);
}

.step-line.active {
  background-color: var(--primary-color);
}

/* 步骤内容 */
.step-content {
  padding: 20rpx;
}

/* 金价卡片 */
.gold-price-card {
  background: var(--primary-gradient);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-left: -20rpx;
  margin-right: -20rpx;
  color: #fff;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(74, 16, 16, 0.3);
}

.gold-price-title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.gold-price-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.gold-price-unit {
  font-size: 24rpx;
  font-weight: normal;
}

.gold-price-update-time {
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 5rpx;
}

.gold-price-source {
  font-size: 20rpx;
  opacity: 0.7;
}

/* 黄金回收流程图样式 */
.process-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  margin: 20rpx -20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.process-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.process-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.process-flow {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.process-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 10rpx 0;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 130rpx;
  position: relative;
}

.step-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.step-number {
  position: absolute;
  top: -8rpx;
  right: 0rpx;
  background: #fff;
  color: #FF4D4F;
  font-size: 20rpx;
  font-weight: bold;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #FF4D4F;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.step-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
  text-align: center;
  line-height: 1.2;
}

.step-desc {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

.process-arrow {
  font-size: 22rpx;
  color: #FF4D4F;
  font-weight: bold;
  flex-shrink: 0;
}

.process-notice {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 15rpx;
}

.notice-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.notice-text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
  flex: 1;
}

/* 黄金类型选择 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: -20rpx;
  margin-right: -20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.gold-types {
  display: flex;
  flex-wrap: wrap;
  margin-left: -20rpx;
  margin-right: -20rpx;
}

.gold-type-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(255, 0, 0, 0.1);
  border: 2rpx solid transparent;
  box-sizing: border-box;
}

.gold-type-item.selected {
  border-color: var(--primary-color);
  background-color: rgba(74, 16, 16, 0.05);
}

.gold-type-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.gold-type-name {
  font-size: 28rpx;
  color: #333;
}

/* 按钮样式 */
.btn-next {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-left: 25%;
  background: var(--primary-gradient);
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(74, 16, 16, 0.3);
  border-radius: 45rpx;
  width: 45%;
  margin-left: 28%;
  margin-right: 0;
}
.btn-submit, .btn-prev, .btn-view-order, .btn-go-home {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-submit, .btn-view-order {
  background-color: #4A1010; /* 深棕色 */
  background-image: linear-gradient(135deg, #6B1919, #4A1010); /* 棕色渐变 */
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(74, 16, 16, 0.3);
}

.btn-sell {
  background: #fff;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  box-shadow: 0 2rpx 5rpx rgba(74, 16, 16, 0.1);
}

.button-group-step1 {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
}

.button-group-step1 .btn-sell, .button-group-step1 {
  width: 48%;
  margin-top: 0;
}

.btn-submit.loading {
  opacity: 0.8;
  background-image: linear-gradient(135deg, #6B1919, #4A1010); /* 保持渐变效果 */
  position: relative;
}

.btn-submit.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-animation 1.5s infinite;
}

@keyframes loading-animation {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.btn-go-home {
  background-color: #f5f5f5;
  color: #666;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
  padding: 0 30rpx;
}

.btn-prev, .btn-submit {
  width: 45%;
  margin-left: 0;
  margin-right: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  display: inline-block;
  margin: 0 6rpx;
  font-size: 28rpx;
}

/* 添加按钮点击效果 */
.btn-submit:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 禁用状态 */
.btn-submit.disabled {
  background: #CCCCCC;
  color: #FFFFFF;
  box-shadow: none;
  pointer-events: none;
}

.btn-prev {
  background-color: #f5f5f5;
  color: #666;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(255, 0, 0, 0.1);
  margin-left: -20rpx;
  margin-right: -20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-label.required::before {
  content: '*';
  color: #FF5252;
  margin-right: 5rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

/* 成色选择器样式 */
.purity-selector {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
}

.purity-display {
  flex: 1;
  color: #333;
}

.purity-display.placeholder {
  color: #999;
}

.purity-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.purity-selector:active .purity-arrow {
  transform: rotate(180deg);
}

.estimated-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.amount-tip {
  font-size: 24rpx;
  color: #999;
}

/* 图片上传 */
.image-uploader {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.image-item, .upload-button {
  width: calc(25% - 20rpx);
  height: 150rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  border: 1rpx dashed #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 上传状态样式 */
.upload-button.uploading {
  background-color: #f5f5f5;
  border-color: #ddd;
  cursor: not-allowed;
}

.upload-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF4D4F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-button.uploading .upload-text {
  color: #666;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

/* 示例图片样式 */
.example-image-container {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.example-image-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.example-image-wrapper {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid #FFD700;
  background-color: #FFF9E6;
}

.example-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.example-image-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(74, 16, 16, 0.7);
  color: #fff;
  font-size: 22rpx;
  text-align: center;
  padding: 8rpx 0;
  font-weight: 500;
}

/* 地址选择器 */
.address-section {
  margin-bottom: 30rpx;
}

.address-selector {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.address-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-right: 30rpx;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.select-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 30rpx;
  color: #999;
}

/* 成功页面 */
.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: var(--primary-gradient);
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
}

.success-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 30rpx;
  border-left: 6rpx solid #fff;
  border-bottom: 6rpx solid #fff;
  transform: translate(-50%, -70%) rotate(-45deg);
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.success-order-id {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 60rpx;
}

.success-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0 20rpx;
}

.btn-view-order, .btn-go-home {
  flex: 1;
  margin-top: 0;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-view-order {
  margin-right: 30rpx;
  background-color: #4A1010;
  background-image: linear-gradient(135deg, #6B1919, #4A1010);
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(74, 16, 16, 0.3);
}

.btn-view-order:active, .btn-go-home:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 地址选择弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
}

.address-modal.show {
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.address-modal.show .modal-mask {
  opacity: 1;
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.address-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.address-list {
  padding-bottom: 30rpx;
}

.address-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.address-info {
  flex: 1;
}

.address-name-phone {
  margin-bottom: 10rpx;
}

.address-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
}

.address-full {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.address-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid var(--primary-color);
  position: relative;
}

.address-check::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.empty-address {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.fee-info {
  margin-top: 10px;
  padding: 10px;
  background: #f8f8f8;
  border-radius: 8px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.fee-item:last-child {
  margin-bottom: 0;
}

.fee-label {
  color: #666;
}

.fee-value {
  color: #333;
  font-weight: 500;
}

.fee-item.final-amount {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
}

.fee-item.final-amount .fee-label,
.fee-item.final-amount .fee-value {
  color: #4A1010;
  font-weight: bold;
  font-size: 16px;
}

/* 成色选择器弹窗样式 */
.purity-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.purity-picker-modal.show {
  opacity: 1;
  visibility: visible;
}

.purity-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.purity-picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.purity-picker-modal.show .purity-picker-content {
  transform: translateY(0);
}

.purity-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.purity-picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.purity-picker-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.purity-picker-list {
  max-height: 60vh;
  overflow-y: auto;
}

.purity-picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.purity-picker-item:last-child {
  border-bottom: none;
}

.purity-picker-item:active {
  background-color: #f5f5f5;
}

.purity-picker-item.selected {
  background-color: rgba(255, 77, 79, 0.1);
}

.purity-picker-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.purity-picker-item.selected .purity-picker-label {
  color: #FF4D4F;
  font-weight: 500;
}

.purity-picker-check {
  font-size: 32rpx;
  color: #FF4D4F;
  font-weight: bold;
}

/* 悬浮客服按钮样式 */
.floating-service-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}

.floating-service-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.6);
}

.service-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: bold;
  font-family: Arial, sans-serif;
}
