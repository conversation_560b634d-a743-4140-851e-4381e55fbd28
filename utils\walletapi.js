/**
 * 钱包相关API请求封装
 */

const app = getApp();

/**
 * 获取完整的API URL
 * @param {string} path - API路径
 * @returns {string} 完整的API URL
 */
const getFullUrl = (path) => {
  return `${app.globalData.apiConfig.baseUrl}${path}`;
};

/**
 * 统一处理请求
 * @param {Object} options - 请求选项
 * @returns {Promise} Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'content-type': options.contentType || 'application/x-www-form-urlencoded',
        'Authorization': options.token ? `Bearer ${options.token}` : ''
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject({
            code: res.statusCode,
            msg: res.data.msg || '请求失败'
          });
        }
      },
      fail: (err) => {
        reject({
          code: -1,
          msg: '网络错误，请检查网络连接',
          error: err
        });
      }
    });
  });
};

/**
 * 上传文件到minio
 * @param {string} filePath - 文件路径
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const uploadFile = (filePath, token) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: getFullUrl('/api/minio/upload'),
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      formData: {
        'bucketName': 'mall' // 统一使用mall bucket
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          console.log('图片上传API响应:', data);

          // 判断API响应：code为0表示成功
          if (data.code === 0 && data.data && data.data.url) {
            resolve({
              code: 0,
              data: {
                url: data.data.url
              },
              message: data.message || '上传成功'
            });
          } else {
            reject({
              code: data.code,
              msg: data.message || '上传失败'
            });
          }
        } catch (e) {
          reject({
            code: -1,
            msg: '解析响应失败',
            error: e
          });
        }
      },
      fail: (err) => {
        reject({
          code: -1,
          msg: '网络错误，请检查网络连接',
          error: err
        });
      }
    });
  });
};

/**
 * 提交充值申请
 * @param {Object} rechargeData - 充值数据
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const submitRecharge = (rechargeData, token) => {
  return request({
    url: getFullUrl('/api/wallet/recharge'),
    method: 'POST',
    data: rechargeData,
    token: token
  });
};

/**
 * 获取钱包余额
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const getWalletBalance = (token) => {
  return request({
    url: getFullUrl('/api/wallet/balance'),
    token: token
  });
};

/**
 * 获取交易记录
 * @param {string} type - 交易类型（可选）
 * @param {number} page - 页码
 * @param {number} pageSize - 每页数量
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const getTransactions = (type, page, pageSize, token) => {
  return request({
    url: getFullUrl('/api/wallet/transactions'),
    data: {
      type: type,
      page: page,
      pageSize: pageSize
    },
    token: token
  });
};

module.exports = {
  uploadFile,
  submitRecharge,
  getWalletBalance,
  getTransactions
};
