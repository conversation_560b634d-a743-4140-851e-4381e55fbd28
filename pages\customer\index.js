// pages/customer/index.js
const app = getApp();

Page({
  data: {
    customers: [], // 客户列表
    filteredCustomers: [], // 筛选后的客户列表
    searchKeyword: '', // 搜索关键词
    totalCustomers: 0, // 总客户数
    activeCustomers: 0, // 活跃客户数
    totalPoints: 0, // 累计返积分
    showDetail: false, // 是否显示客户详情
    currentCustomer: null, // 当前查看的客户
    loading: false, // 加载状态
    currentTab: 'all', // 当前选中的标签页：all-全部, level1-一级下级, level2-二级下级
    level1Customers: [], // 一级下级客户
    level2Customers: [], // 二级下级客户
    allCustomers: [] // 所有客户（一级+二级）
  },

  onLoad: function (options) {
    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/my/my'
        });
      }, 1500);
      return;
    }

    // 加载客户数据
    this.loadCustomerData();
  },

  onShow: function () {
    // 刷新客户数据
    this.loadCustomerData();
  },

  // 加载客户数据
  loadCustomerData: function () {
    this.setData({
      loading: true
    });

    wx.showLoading({
      title: '加载客户数据...',
      mask: true
    });

    // 并行获取一级下级和二级下级数据
    Promise.all([
      this.getLevel1Customers(),
      this.getLevel2Customers()
    ])
      .then(([level1Data, level2Data]) => {
        // 处理一级下级数据
        const level1Customers = this.processCustomerData(level1Data, 1);

        // 处理二级下级数据
        const level2Customers = this.processCustomerData(level2Data, 2);

        // 合并所有客户数据
        const allCustomers = [...level1Customers, ...level2Customers];

        // 计算统计数据
        const totalCustomers = allCustomers.length;
        const activeCustomers = this.calculateActiveCustomers(allCustomers);
        const totalPoints = this.calculateTotalPoints(allCustomers);

        this.setData({
          level1Customers,
          level2Customers,
          allCustomers,
          customers: allCustomers,
          filteredCustomers: allCustomers,
          totalCustomers,
          activeCustomers,
          totalPoints,
          loading: false
        });

        wx.hideLoading();
      })
      .catch((error) => {
        console.error('加载客户数据失败:', error);
        wx.hideLoading();

        this.setData({
          loading: false
        });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 获取一级下级客户
  getLevel1Customers: function () {
    return new Promise((resolve, reject) => {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/user-relation/${userInfo.account}/children`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('一级下级API响应:', res);
          if (res.statusCode === 200 && res.data.code === 200) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.message || '获取一级下级失败'));
          }
        },
        fail: (error) => {
          console.error('获取一级下级请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 获取二级下级客户
  getLevel2Customers: function () {
    return new Promise((resolve, reject) => {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/user-relation/${userInfo.account}/sub-children`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('二级下级API响应:', res);
          if (res.statusCode === 200 && res.data.code === 200) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.message || '获取二级下级失败'));
          }
        },
        fail: (error) => {
          console.error('获取二级下级请求失败:', error);
          reject(new Error('网络错误，请检查网络连接'));
        }
      });
    });
  },

  // 处理客户数据
  processCustomerData: function (rawData, customerLevel) {
    if (!Array.isArray(rawData)) {
      return [];
    }

    return rawData.map((item, index) => {
      return {
        id: item.userId || `customer_${customerLevel}_${index}`,
        userId: item.userId,
        parentId: item.parentId,
        nickname: this.maskUserId(item.userId) || `客户${item.userId?.slice(-4) || index}`,
        avatar: '/images/default-avatar.png',
        phone: this.maskPhone(item.userId) || '未绑定手机',
        level: customerLevel, // 1-一级下级, 2-二级下级
        registerTime: this.formatTime(item.createTime) || '未知',
        lastActiveTime: this.formatTime(item.updateTime) || '未知',
        totalSpent: '0.00', // API暂未提供，使用默认值
        points: '0', // API暂未提供，使用默认值
        orderCount: 0, // API暂未提供，使用默认值
        inviter: customerLevel === 1 ? item.parentId : '上级用户',
        recentOrders: [], // API暂未提供，使用空数组
        relationId: item.id,
        createTime: item.createTime,
        updateTime: item.updateTime
      };
    });
  },

  // 计算活跃客户数（最近30天有活动的客户）
  calculateActiveCustomers: function (customers) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return customers.filter(customer => {
      if (!customer.lastActiveTime || customer.lastActiveTime === '未知') {
        return false;
      }
      const lastActiveDate = new Date(customer.lastActiveTime);
      return lastActiveDate >= thirtyDaysAgo;
    }).length;
  },

  // 计算累计返积分
  calculateTotalPoints: function (customers) {
    return customers.reduce((sum, customer) => {
      return sum + parseInt(customer.points || 0);
    }, 0);
  },

  // 格式化时间
  formatTime: function (timeString) {
    if (!timeString) return '未知';

    try {
      const date = new Date(timeString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return '未知';
    }
  },

  // 脱敏用户ID显示
  maskUserId: function (userId) {
    if (!userId || typeof userId !== 'string') return '客户';

    if (userId.length <= 4) {
      return userId;
    }

    // 显示前2位和后2位，中间用*代替
    const start = userId.slice(0, 2);
    const end = userId.slice(-2);
    const middle = '*'.repeat(Math.max(0, userId.length - 4));
    return `${start}${middle}${end}`;
  },

  // 脱敏手机号显示
  maskPhone: function (phone) {
    if (!phone || typeof phone !== 'string') return '未绑定手机';

    // 如果是手机号格式，进行脱敏
    if (/^1[3-9]\d{9}$/.test(phone)) {
      return phone.slice(0, 3) + '****' + phone.slice(-4);
    }

    // 否则当作用户ID处理
    return this.maskUserId(phone);
  },

  // 标签页切换
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    let customers = [];

    switch (tab) {
      case 'all':
        customers = this.data.allCustomers;
        break;
      case 'level1':
        customers = this.data.level1Customers;
        break;
      case 'level2':
        customers = this.data.level2Customers;
        break;
      default:
        customers = this.data.allCustomers;
    }

    this.setData({
      currentTab: tab,
      customers: customers,
      filteredCustomers: customers,
      searchKeyword: '' // 切换标签页时清空搜索
    });
  },

  // 搜索输入
  onSearchInput: function (e) {
    const keyword = e.detail.value.trim().toLowerCase();

    this.setData({
      searchKeyword: keyword
    });

    this.filterCustomers(keyword);
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchKeyword: ''
    });

    this.filterCustomers('');
  },

  // 筛选客户
  filterCustomers: function (keyword) {
    if (!keyword) {
      // 如果没有关键词，显示所有客户
      this.setData({
        filteredCustomers: this.data.customers
      });
      return;
    }

    // 根据昵称或手机号筛选
    const filtered = this.data.customers.filter(customer => {
      return (
        (customer.nickname && customer.nickname.toLowerCase().includes(keyword)) ||
        (customer.phone && customer.phone.includes(keyword))
      );
    });

    this.setData({
      filteredCustomers: filtered
    });
  },

  // 显示客户详情
  showCustomerDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    const customer = this.data.customers.find(item => item.id === id);

    if (customer) {
      this.setData({
        showDetail: true,
        currentCustomer: customer
      });
    }
  },

  // 隐藏客户详情
  hideCustomerDetail: function () {
    this.setData({
      showDetail: false
    });
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    // 重新加载客户数据
    this.loadCustomerData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  }
});
