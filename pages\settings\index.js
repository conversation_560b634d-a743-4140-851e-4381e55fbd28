// pages/settings/index.js
const app = getApp();

Page({
  data: {
    isLogin: false,
    cacheSize: '0KB'
  },

  onLoad: function (options) {
    // 检查登录状态
    if (app.globalData.userInfo && app.globalData.userInfo.isLogin) {
      this.setData({
        isLogin: true
      });
    }
    
    // 获取缓存大小
    this.getCacheSize();
  },
  
  // 获取缓存大小
  getCacheSize: function() {
    wx.getStorageInfo({
      success: (res) => {
        const size = res.currentSize;
        let sizeStr = '';
        
        if (size < 1024) {
          sizeStr = size + 'KB';
        } else {
          sizeStr = (size / 1024).toFixed(2) + 'MB';
        }
        
        this.setData({
          cacheSize: sizeStr
        });
      }
    });
  },
  
  // 清除缓存
  clearCache: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清除缓存吗？',
      success: (res) => {
        if (res.confirm) {
          // 保留登录信息，清除其他缓存
          const userInfo = wx.getStorageSync('userInfo');
          
          wx.clearStorageSync();
          
          if (userInfo && userInfo.isLogin) {
            wx.setStorageSync('userInfo', userInfo);
          }
          
          this.getCacheSize();
          
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          });
        }
      }
    });
  },
  
  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除全局用户信息
          app.globalData.userInfo = null;
          
          // 清除本地存储的用户信息
          wx.removeStorageSync('userInfo');
          
          // 更新页面状态
          this.setData({
            isLogin: false
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 返回到我的页面
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/my/my'
            });
          }, 1500);
        }
      }
    });
  },
  
  // 跳转到账号与安全页面
  goToAccountSecurity: function() {
    wx.navigateTo({
      url: '/pages/settings/account-security'
    });
  },
  
  // 跳转到地址管理页面
  goToAddressManage: function() {
    wx.navigateTo({
      url: '/pages/settings/address-manage'
    });
  },
  
  // 跳转到消息通知设置页面
  goToNotificationSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/notification'
    });
  },
  
  // 跳转到关于我们页面
  goToAboutUs: function() {
    wx.navigateTo({
      url: '/pages/settings/about'
    });
  },
  
  // 跳转到意见反馈页面
  goToFeedback: function() {
    wx.navigateTo({
      url: '/pages/settings/feedback'
    });
  },
  
  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    });
  },
  
  // 显示用户服务协议
  showUserAgreement: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
})
