// app.js
App({
  onLaunch: function () {
    // 检查本地存储中是否有用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      this.globalData.userInfo = userInfo;
    }

    // 获取系统信息
    wx.getSystemInfo({
      success: e => {
        this.globalData.systemInfo = e;
        this.globalData.statusBarHeight = e.statusBarHeight;
        let capsule = wx.getMenuButtonBoundingClientRect();
        if (capsule) {
          this.globalData.customBarHeight = capsule.bottom + capsule.top - e.statusBarHeight;
        } else {
          this.globalData.customBarHeight = e.statusBarHeight + 44;
        }
      }
    });

    // 获取当前页面路径中的邀请参数
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options && options.inviter) {
        this.globalData.inviter = options.inviter;
      }
    }
  },

  globalData: {
    userInfo: null,
    inviter: null,
    systemInfo: null,
    statusBarHeight: 0,
    customBarHeight: 0,
    // API相关配置
    apiConfig: {
      baseUrl: 'https://www.iejhsgold.cn',
      expressApiKey: 'c844cf198dc2decd1e46c05abd2d04b6',
      user: {
        login: '/api/user/login',
        register: '/api/user',
        info: '/api/user/info',
        update: '/api/user/update',
        resetPassword: '/api/user/resetPassword',
        superior: '/api/user/superior',
        bindSuperior: '/api/user/bind-superior',
        inviteList: '/api/user/invite-list'
      },
      order: {
        list: '/api/order/list',
        detail: '/api/order/detail',
        create: '/api/order/create'
      },
      gold: {
        price: '/api/gold/price',
        recycle: '/api/gold/recycle'
      },
      wallet: {
        balance: '/api/wallet/balance',
        recharge: '/api/wallet/recharge',
        withdraw: '/api/wallet/withdraw',
        transactions: '/api/wallet/transactions',
        upload: '/api/wallet/upload'
      }
    }
  }
})
