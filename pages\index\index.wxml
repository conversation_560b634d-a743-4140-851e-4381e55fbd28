<!--pages/index/index.wxml-->
<view class="container">
  <!-- 金价展示区域 -->
  <view class="gold-price-card">
    <view class="gold-price-title">卖金上爱尔嘉 黄金秒变现</view>

    <view class="service-tags">
      <view class="service-tag">
        <image class="tag-icon" src="/images/check-icon.png"></image>
        <text class="tag-text">支持预付</text>
      </view>
      <view class="service-tag">
        <image class="tag-icon" src="/images/check-icon.png"></image>
        <text class="tag-text">秒速到账</text>
      </view>
      <view class="service-tag">
        <image class="tag-icon" src="/images/check-icon.png"></image>
        <text class="tag-text">实时报价</text>
      </view>
      <view class="service-tag">
        <image class="tag-icon" src="/images/check-icon.png"></image>
        <text class="tag-text">精准检测</text>
      </view>
    </view>

    <view class="price-info-container">
      <view class="platform-price">
        <view class="price-label-1">平台累计收金(千克)</view>
        <view class="price-value">{{platformStats.totalGold || '0'}}</view>
      </view>
      <view class="city-price">
        <swiper class="price-swiper" autoplay="{{true}}" interval="3000" duration="500" vertical="{{true}}" circular="{{true}}" display-multiple-items="1">
          <swiper-item wx:for="{{cityStats.records}}" wx:key="index">
            <view class="price-item">
              <text>{{item.city}}{{item.time}} 卖出 {{item.amount}}克</text>
            </view>
          </swiper-item>
          <!-- 默认数据，当cityStats.records为空时显示 -->
          <swiper-item wx:if="{{!cityStats.records || cityStats.records.length === 0}}">
            <view class="price-item">
              <text>重庆市昨日*黄 卖出 {{cityStats.yesterdaySell || '27.79'}}克</text>
            </view>
          </swiper-item>
          <swiper-item wx:if="{{!cityStats.records || cityStats.records.length === 0}}">
            <view class="price-item">
              <text>送宁到* 卖出 {{cityStats.todaySell || '5.44'}}克</text>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>

  </view>

  <!-- 实时金价信息 -->
  <view class="price-section" style="height: 478rpx; display: block; box-sizing: border-box">
    <view class="price-header">
      <view class="price-tag">金</view>
      <text class="price-no">更新时间: {{goldPrice.updateTime}}</text>
    </view>

    <view class="real-price">
      <text class="price-label">实时金价：(元/克)</text>
      <text class="price-amount">{{goldPrice.sellPrice}}</text>
    </view>

    <view class="price-info">
      <view class="price-info-item">
        <text class="info-label">买入价：</text>
        <text class="info-value">{{goldPrice.buyPrice}}元/克</text>
      </view>
      <view class="price-info-item">
        <text class="info-label">卖出价：</text>
        <text class="info-value">{{goldPrice.sellPrice}}元/克</text>
      </view>
    </view>
  </view>

  <!-- 买入口 -->
  <view class="trade-buttons">
    <view class="trade-button sell-button" bindtap="goToRecycle">卖黄金</view>
  </view>


  <!-- 参考行情区域 -->
  <view class="market-section">
    <view class="section-header">
      <text class="section-title">参考行情</text>
      <text class="unit-text">单位：元/克</text>
    </view>

    <!-- 表格样式的行情展示 -->
    <view class="market-table">
      <!-- 表头 -->
      <view class="table-header">
        <view class="table-cell">商品</view>
        <view class="table-cell">回购</view>
        <view class="table-cell">销售</view>
        <view class="table-cell">高低</view>
      </view>

      <!-- 数据行 -->
      <view class="table-row" wx:for="{{marketData}}" wx:key="id">
        <view class="table-cell product-name">{{item.name}}</view>
        <view class="table-cell buy-price">{{item.buyPrice}}</view>
        <view class="table-cell sell-price">{{item.sellPrice}}</view>
        <view class="table-cell high-low">
          <view class="high-price">{{item.maxPrice}}</view>
          <view class="low-price">{{item.minPrice}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 为什么选择爱尔嘉 -->
  <view class="why-choose-section">
    <view class="why-choose-title">为什么选择爱尔嘉</view>

    <view class="advantage-item" bindtap="goToAdvantageDetail" data-type="order">
      <view class="advantage-content">
        <view class="advantage-title">一键下单 安全便捷</view>
        <view class="advantage-desc">足不出户完成黄金交易，一键下单顺丰上门，平台自动验货保险，全程保障金料安全</view>
      </view>
      <view class="advantage-icon-container">
        <view class="advantage-icon order-icon">
          <image src="/images/icons/order.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="advantage-item" bindtap="goToAdvantageDetail" data-type="price">
      <view class="advantage-content">
        <view class="advantage-title">下单锁价 稳守价格空间</view>
        <view class="advantage-desc">手机实时查看价，随时下单锁价，支持实时锁价，保证买卖不受市场波动影响</view>
      </view>
      <view class="advantage-icon-container">
        <view class="advantage-icon price-icon">
          <image src="/images/icons/price.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="advantage-item" bindtap="goToAdvantageDetail" data-type="payment">
      <view class="advantage-content">
        <view class="advantage-title">支持延期结价 周转且价格满意</view>
        <view class="advantage-desc">想将旧料变现，但当日金价不满意，可先卖获得预付款，等金价在合适位置时定价结算</view>
      </view>
      <view class="advantage-icon-container">
        <view class="advantage-icon payment-icon">
          <image src="/images/icons/payment.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="advantage-item" bindtap="goToAdvantageDetail" data-type="testing">
      <view class="advantage-content">
        <view class="advantage-title">精准检测 100%真实可溯源</view>
        <view class="advantage-desc">按照国家标准规范化黄金检测，全程可追溯，保证成色报告精确无误</view>
      </view>
      <view class="advantage-icon-container">
        <view class="advantage-icon testing-icon">
          <image src="/images/icons/testing.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 悬浮客服按钮 -->
  <view class="floating-service-btn" bindtap="contactService">
    <view class="service-icon">?</view>
  </view>

</view>