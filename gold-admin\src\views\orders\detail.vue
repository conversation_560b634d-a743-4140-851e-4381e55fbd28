<template>
  <div class="app-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-button type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div v-if="orderDetail" class="order-detail">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions title="订单信息" :column="1" border>
              <el-descriptions-item label="订单号">
                {{ orderDetail.orderId }}
              </el-descriptions-item>
              <el-descriptions-item label="用户账号">
                {{ orderDetail.account }}
              </el-descriptions-item>
              <el-descriptions-item label="订单状态">
                <el-tag :type="getStatusType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(orderDetail.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(orderDetail.updateTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="收货信息" :column="1" border v-loading="loadingAddress">
              <el-descriptions-item label="收货人">
                {{ getReceiverName() }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ getReceiverPhone() }}
              </el-descriptions-item>
              <el-descriptions-item label="收货地址">
                {{ getFullAddress() }}
              </el-descriptions-item>
              <el-descriptions-item label="邮政编码" v-if="addressInfo && addressInfo.postCode">
                {{ addressInfo.postCode }}
              </el-descriptions-item>
              <el-descriptions-item label="默认地址" v-if="addressInfo">
                <el-tag :type="addressInfo.isDefault === 1 ? 'success' : 'info'" size="small">
                  {{ addressInfo.isDefault === 1 ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 贵金属信息 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-descriptions title="贵金属信息" :column="1" border>
              <el-descriptions-item label="贵金属类型">
                {{ getGoldTypeText(orderDetail.goldType) }}
              </el-descriptions-item>
              <el-descriptions-item label="贵金属状况">
                {{ orderDetail.goldCondition }}
              </el-descriptions-item>
              <el-descriptions-item label="成色">
                {{ orderDetail.purity }}
              </el-descriptions-item>
              <el-descriptions-item label="描述">
                {{ orderDetail.description }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="价格信息" :column="1" border>
              <el-descriptions-item label="预估重量">
                {{ orderDetail.estimatedWeight }}g
              </el-descriptions-item>
              <el-descriptions-item label="预估价格">
                ¥{{ formatPrice(orderDetail.estimatedPrice) }}
              </el-descriptions-item>
              <el-descriptions-item label="最终价格">
                <span v-if="orderDetail.finalPrice">¥{{ formatPrice(orderDetail.finalPrice) }}</span>
                <span v-else class="text-muted">待定</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 详细检测结果 -->
        <div v-if="quotationInfo && quotationInfo.hasQuotation" style="margin-top: 20px;">
          <h3>检测结果详情</h3>

          <!-- 加载中 -->
          <div v-if="loadingQuotation" style="text-align: center; padding: 40px 0;">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span style="margin-left: 10px;">正在获取检测结果...</span>
          </div>

          <!-- 检测结果项列表 -->
          <div v-else class="quotation-display">
            <div v-for="(quotation, quotationIndex) in quotationInfo.quotations" :key="quotation.id" class="quotation-record">
              <el-card class="quotation-card">
                <template #header>
                  <div class="quotation-header">
                    <span class="quotation-title">报价记录 {{ quotationIndex + 1 }}</span>
                    <span class="quotation-time">{{ quotation.createTimeFormatted }}</span>
                  </div>
                </template>

                <!-- 检测项展示 -->
                <div class="quotation-items-display">
                  <div v-for="(item, itemIndex) in quotation.items" :key="item.id" class="quotation-item-display">
                    <el-card class="item-display-card">
                      <template #header>
                        <div class="item-display-header">
                          <span class="item-title">检测项 {{ itemIndex + 1 }}</span>
                          <el-tag type="warning" size="small">{{ item.goldTypeText }}</el-tag>
                        </div>
                      </template>

                      <div class="item-content-display">
                        <!-- 左侧：检测图片 -->
                        <div class="item-image-display">
                          <el-image
                            v-if="item.imageUrl"
                            :src="item.imageUrl"
                            style="width: 100px; height: 100px;"
                            :preview-src-list="[item.imageUrl]"
                            fit="cover"
                          />
                          <div v-else class="no-image-display">
                            <el-icon><Picture /></el-icon>
                            <span>暂无图片</span>
                          </div>
                        </div>

                        <!-- 右侧：检测数据 -->
                        <div class="item-data-display">
                          <el-descriptions :column="2" size="small" border>
                            <el-descriptions-item label="单价">¥{{ item.price }}/g</el-descriptions-item>
                            <el-descriptions-item label="含金量">{{ item.goldContent }}</el-descriptions-item>
                            <el-descriptions-item label="克重">{{ item.weight }}g</el-descriptions-item>
                            <el-descriptions-item label="熔后重">{{ item.meltedWeight }}g</el-descriptions-item>
                            <el-descriptions-item label="纯金重">{{ item.pureGoldWeight }}g</el-descriptions-item>
                            <el-descriptions-item label="小计">
                              <span style="color: #ff6b35; font-weight: bold; font-size: 16px;">¥{{ item.itemTotal }}</span>
                            </el-descriptions-item>
                          </el-descriptions>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </div>

                <!-- 单个报价的费用明细 -->
                <div class="quotation-fee-display">
                  <h4>本次报价明细</h4>
                  <el-descriptions :column="2" size="small" border>
                    <el-descriptions-item label="检测总价">¥{{ quotation.quotationTotal }}</el-descriptions-item>
                    <el-descriptions-item label="运费" v-if="quotation.shippingFee !== '0.00'">-¥{{ quotation.shippingFee }}</el-descriptions-item>
                    <el-descriptions-item label="保费" v-if="quotation.insuranceFee !== '0.00'">-¥{{ quotation.insuranceFee }}</el-descriptions-item>
                    <el-descriptions-item label="服务费" v-if="quotation.serviceFee !== '0.00'">-¥{{ quotation.serviceFee }}</el-descriptions-item>
                    <el-descriptions-item label="其他项目费用" v-if="quotation.otherItems !== '0.00'">-¥{{ quotation.otherItems }}</el-descriptions-item>
                    <el-descriptions-item label="本次金额" span="2">
                      <span style="color: #52c41a; font-weight: bold; font-size: 18px;">¥{{ quotation.quotationFinalAmount }}</span>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-card>
            </div>

            <!-- 总费用明细（多个报价时显示） -->
            <div v-if="quotationInfo.quotationCount > 1" class="total-fee-display">
              <el-card class="total-fee-card">
                <template #header>
                  <h3 style="margin: 0; color: #1890ff;">总费用明细</h3>
                </template>
                <el-descriptions :column="2" size="default" border>
                  <el-descriptions-item label="检测总价">¥{{ quotationInfo.totalAmount }}</el-descriptions-item>
                  <el-descriptions-item label="总运费" v-if="quotationInfo.totalShippingFee !== '0.00'">-¥{{ quotationInfo.totalShippingFee }}</el-descriptions-item>
                  <el-descriptions-item label="总保费" v-if="quotationInfo.totalInsuranceFee !== '0.00'">-¥{{ quotationInfo.totalInsuranceFee }}</el-descriptions-item>
                  <el-descriptions-item label="总服务费" v-if="quotationInfo.totalServiceFee !== '0.00'">-¥{{ quotationInfo.totalServiceFee }}</el-descriptions-item>
                  <el-descriptions-item label="总其他项目费用" v-if="quotationInfo.totalOtherItems !== '0.00'">-¥{{ quotationInfo.totalOtherItems }}</el-descriptions-item>
                  <el-descriptions-item label="最终总金额" span="2">
                    <span style="color: #ff4d4f; font-weight: bold; font-size: 20px;">¥{{ quotationInfo.finalAmount }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 简化检测结果（当没有详细检测结果时） -->
        <div v-else-if="orderDetail.inspectionResult" style="margin-top: 20px;">
          <el-descriptions title="检测结果" :column="1" border>
            <el-descriptions-item label="检测结果">
              {{ orderDetail.inspectionResult }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 快递信息 -->
        <div v-if="expressInfo && (expressInfo.expressCompany || expressInfo.trackingNumber)" style="margin-top: 20px;">
          <h3>快递信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="快递公司" v-if="expressInfo.expressCompany">
              {{ getExpressCompanyName(expressInfo.expressCompany) }}
            </el-descriptions-item>
            <el-descriptions-item label="快递单号" v-if="expressInfo.trackingNumber">
              {{ expressInfo.trackingNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="取件时间" v-if="expressInfo.pickupTime" span="2">
              {{ formatTime(expressInfo.pickupTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" v-if="expressInfo.remark" span="2">
              {{ expressInfo.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 图片展示 -->
        <div v-if="orderDetail.imageList && orderDetail.imageList.length > 0" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in orderDetail.imageList"
              :key="index"
              :src="image"
              style="width: 200px; height: 200px; margin: 10px;"
              :preview-src-list="orderDetail.imageList"
              fit="cover"
            />
          </div>
        </div>
        <!-- 兼容旧的单图片字段 -->
        <div v-else-if="orderDetail.imageBase64" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <el-image
            :src="orderDetail.imageBase64"
            style="width: 200px; height: 200px; margin: 10px;"
            :preview-src-list="[orderDetail.imageBase64]"
            fit="cover"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons" style="margin-top: 30px;">
          <el-button
            v-if="orderDetail.status === 1"
            size="small"
            type="success"
            @click="handleFillExpressInfo(orderDetail)"
          >
            填写快递单号
          </el-button>
          <el-button
            v-if="orderDetail.status === 2"
            size="small"
            type="warning"
            @click="handleSignReceived(orderDetail)"
          >
            已签收
          </el-button>
          <el-button
            v-if="orderDetail.status === 3"
            size="small"
            type="primary"
            @click="handleInspection(orderDetail)"
          >
            检测
          </el-button>
          <el-button
            v-if="orderDetail.status === 5"
            size="small"
            type="success"
            @click="handleComplete(orderDetail)"
          >
            完成订单
          </el-button>
          <!-- 已送到店按钮 - 只在状态1(已下单)时显示 -->
          <el-button
            v-if="orderDetail.status === 1"
            size="small"
            type="success"
            @click="handleDeliveredToStore(orderDetail)"
          >
            已送到店
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 已送到店对话框 -->
    <el-dialog
      title="设置最终价格"
      v-model="deliveredDialogVisible"
      width="400px"
    >
      <el-form
        ref="deliveredFormRef"
        :model="deliveredForm"
        :rules="deliveredRules"
        label-width="100px"
      >
        <el-form-item label="订单号">
          <el-input v-model="deliveredForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="最终价格" prop="finalPrice">
          <el-input-number
            v-model="deliveredForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入最终价格"
          />
        </el-form-item>
        <el-form-item label="总克重" prop="totalWeight">
          <el-input-number
            v-model="deliveredForm.totalWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入总克重"
          />
          <span style="margin-left: 8px; color: #999;">克</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deliveredDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmDelivered" :loading="deliveredLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写快递信息对话框 -->
    <el-dialog
      title="填写快递信息"
      v-model="expressDialogVisible"
      width="500px"
    >
      <el-form
        ref="expressFormRef"
        :model="expressForm"
        :rules="expressRules"
        label-width="120px"
      >
        <el-form-item label="订单号">
          <el-input v-model="expressForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="快递公司" prop="expressCompany">
          <el-select
            v-model="expressForm.expressCompany"
            placeholder="请选择快递公司"
            style="width: 100%"
          >
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="申通快递" value="STO" />
            <el-option label="韵达速递" value="YD" />
            <el-option label="百世快递" value="BEST" />
            <el-option label="德邦快递" value="DBL" />
            <el-option label="京东快递" value="JD" />
            <el-option label="邮政EMS" value="EMS" />
            <el-option label="天天快递" value="HHTT" />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="expressForm.trackingNumber"
            placeholder="请输入快递单号"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="expressForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="expressDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmExpressInfo" :loading="expressSubmitLoading">
            确认取件
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 检测结果录入对话框 -->
    <el-dialog
      title="录入检测结果"
      v-model="inspectionDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="inspectionFormRef"
        :model="inspectionForm"
        :rules="inspectionRules"
        label-width="120px"
      >
        <el-form-item label="订单号">
          <el-input v-model="inspectionForm.orderId" readonly />
        </el-form-item>

        <!-- 检测结果项列表 -->
        <el-form-item label="检测结果项">
          <div class="inspection-items">
            <div
              v-for="(item, index) in inspectionForm.items"
              :key="index"
              class="inspection-item"
            >
              <el-card class="item-card">
                <template #header>
                  <div class="card-header">
                    <span>检测项 {{ index + 1 }}</span>
                    <el-button
                      v-if="inspectionForm.items.length > 1"
                      type="danger"
                      size="small"
                      @click="removeInspectionItem(index)"
                      :icon="Delete"
                    >
                      删除
                    </el-button>
                  </div>
                </template>

                <!-- 第一行：基本信息 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.goldType`"
                      :rules="[{ required: true, message: '请输入黄金类型', trigger: 'blur' }]"
                      label="黄金类型"
                      label-width="100px"
                    >
                      <el-input
                        v-model="item.goldType"
                        placeholder="请输入黄金类型，如：Au9999、Au999、足金等"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.price`"
                      :rules="[{ required: true, message: '请输入价格', trigger: 'blur' }]"
                      label="价格"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.price"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入价格"
                      />
                      <span class="unit">元</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.goldContent`"
                      :rules="[{ required: true, message: '请输入含金量', trigger: 'blur' }]"
                      label="含金量"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.goldContent"
                        :precision="2"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                        placeholder="请输入含金量"
                      />
                      <span class="unit">%</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行：重量信息 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.weight`"
                      :rules="[{ required: true, message: '请输入克重', trigger: 'blur' }]"
                      label="克重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.weight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入克重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.meltedWeight`"
                      :rules="[{ required: true, message: '请输入熔后重', trigger: 'blur' }]"
                      label="熔后重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.meltedWeight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入熔后重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.pureGoldWeight`"
                      :rules="[{ required: true, message: '请输入纯金重', trigger: 'blur' }]"
                      label="纯金重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.pureGoldWeight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入纯金重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第三行：检测图片 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="24">
                    <el-form-item
                      label="检测图片"
                      label-width="100px"
                    >
                      <div class="image-upload-container">
                        <el-upload
                          class="image-uploader"
                          :action="uploadAction"
                          :headers="uploadHeaders"
                          :show-file-list="false"
                          :on-success="(response) => handleImageSuccess(response, index)"
                          :on-error="handleImageError"
                          :before-upload="beforeImageUpload"
                          accept="image/*"
                        >
                          <img v-if="item.imageUrl" :src="item.imageUrl" class="uploaded-image" />
                          <div v-else class="upload-placeholder">
                            <el-icon class="image-uploader-icon"><Plus /></el-icon>
                            <div class="upload-text">点击上传检测图片</div>
                          </div>
                        </el-upload>
                        <div class="upload-tips">
                          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>

            <el-button
              type="primary"
              @click="addInspectionItem"
              :icon="Plus"
              style="width: 100%; margin-top: 10px;"
            >
              添加检测结果项
            </el-button>
          </div>
        </el-form-item>

        <!-- 费用信息 -->
        <el-divider content-position="left">费用信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="总运费" prop="shippingFee">
              <el-input-number
                v-model="inspectionForm.shippingFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入总运费"
              />
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保费" prop="insuranceFee">
              <el-input-number
                v-model="inspectionForm.insuranceFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入保费"
              />
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务费" prop="serviceFee">
              <el-input-number
                v-model="inspectionForm.serviceFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入服务费"
              />
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 其他项目信息 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="其他项目" prop="otherItems">
              <el-input-number
                v-model="inspectionForm.otherItems"
                type="textarea"
                :rows="3"
                placeholder="请输入其他项目信息（可选）"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inspectionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInspection" :loading="inspectionSubmitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus, Loading, Picture } from '@element-plus/icons-vue'
import { getOrderDetail, updateOrderStatus, getExpressInfo, pickupOrder, updateRecycleOrderStatus, updateInspectionResult, setRecycleFinalPrice, setFinalPrice, uploadFile, createQuotation, getQuotationByOrderId, getAddressDetail } from '@/api/orders'
import { parseTime } from '@/utils'
import { useUserStore } from '@/stores/user'
import { processRebateWithLoading } from '@/utils/rebate'

const route = useRoute()
const orderDetail = ref(null)
const quotationInfo = ref(null) // 检测结果信息
const addressInfo = ref(null) // 地址详情信息
const loading = ref(true)
const loadingQuotation = ref(false) // 检测结果加载状态
const loadingAddress = ref(false) // 地址加载状态
const expressInfo = ref(null)
const addressLoading = ref(false)

// 已送到店对话框相关 (从recycle.vue复制)
const deliveredDialogVisible = ref(false)
const deliveredLoading = ref(false)
const deliveredForm = ref({
  orderId: '',
  finalPrice: null,
  totalWeight: null
})
const deliveredRules = {
  finalPrice: [
    { required: true, message: '请输入最终价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最终价格必须大于等于0', trigger: 'blur' }
  ],
  totalWeight: [
    { required: true, message: '请输入总克重', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总克重必须大于0', trigger: 'blur' }
  ]
}

// 快递信息对话框相关 (从recycle.vue复制)
const expressDialogVisible = ref(false)
const expressSubmitLoading = ref(false)
const expressForm = ref({
  orderId: '',
  expressCompany: '',
  trackingNumber: '',
  remark: ''
})
const expressRules = {
  expressCompany: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: '请输入快递单号', trigger: 'blur' },
    { min: 8, max: 30, message: '快递单号长度应在8-30个字符之间', trigger: 'blur' }
  ]
}

// 检测结果对话框相关 (从recycle.vue复制)
const inspectionDialogVisible = ref(false)
const inspectionSubmitLoading = ref(false)
const userStore = useUserStore()

// 上传相关配置
const uploadAction = computed(() => {
  return import.meta.env.DEV ? '/api/minio/upload' : 'https://www.iejhsgold.cn/api/minio/upload'
})
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

const inspectionForm = ref({
  orderId: '',
  items: [
    {
      goldType: '',
      price: null,
      weight: null,
      meltedWeight: null,
      pureGoldWeight: null,
      goldContent: null,
      imageUrl: ''
    }
  ],
  shippingFee: 0,
  insuranceFee: 0,
  serviceFee: 0,
  otherItems: ''
})

const inspectionRules = {
  shippingFee: [
    { type: 'number', min: 0, message: '总运费必须大于等于0', trigger: 'blur' }
  ],
  insuranceFee: [
    { type: 'number', min: 0, message: '保费必须大于等于0', trigger: 'blur' }
  ],
  serviceFee: [
    { type: 'number', min: 0, message: '服务费必须大于等于0', trigger: 'blur' }
  ]
}



async function getOrderDetails() {
  loading.value = true
  try {
    const response = await getOrderDetail(route.params.id)
    if (response.code === 200) {
      orderDetail.value = response.data
      if (orderDetail.value.orderId) {
        getExpressDetails(orderDetail.value.orderId)

        // 如果有地址ID，获取地址详情
        if (orderDetail.value.addressId) {
          getAddressDetails(orderDetail.value.addressId)
        }

        // 如果订单状态为4、5、6，获取检测结果
        if (orderDetail.value.status === 4 || orderDetail.value.status === 5 || orderDetail.value.status === 6) {
          getQuotationDetails(orderDetail.value.orderId)
        }
      }
    } else {
      ElMessage.error(response.message || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取地址详情
async function getAddressDetails(addressId) {
  if (!addressId) {
    console.log('没有地址ID，跳过地址详情获取')
    return
  }

  loadingAddress.value = true
  try {
    const response = await getAddressDetail(addressId)
    console.log('地址详情API响应:', response)

    if (response.code === 200 && response.data) {
      addressInfo.value = response.data
      console.log('地址详情数据:', addressInfo.value)
    } else {
      console.error('获取地址详情失败:', response.message)
      // 地址详情获取失败不影响主要功能，只记录错误
    }
  } catch (error) {
    console.error('获取地址详情请求失败:', error)
    // 地址详情获取失败不影响主要功能，只记录错误
  } finally {
    loadingAddress.value = false
  }
}

// 获取检测结果详情
async function getQuotationDetails(orderId) {
  if (!orderId) {
    console.log('没有订单ID，跳过检测结果获取')
    return
  }

  loadingQuotation.value = true
  try {
    const response = await getQuotationByOrderId(orderId)
    console.log('检测结果API响应:', response)

    if (response.code === 200 && response.data) {
      quotationInfo.value = processQuotationData(response.data)
      console.log('处理后的检测结果:', quotationInfo.value)
    } else {
      console.error('获取检测结果失败:', response.message)
      // 检测结果获取失败不影响主要功能，只记录错误
    }
  } catch (error) {
    console.error('获取检测结果请求失败:', error)
    // 检测结果获取失败不影响主要功能，只记录错误
  } finally {
    loadingQuotation.value = false
  }
}

// 处理检测结果数据
function processQuotationData(quotationDataArray) {
  if (!quotationDataArray || !Array.isArray(quotationDataArray) || quotationDataArray.length === 0) {
    return {
      quotations: [],
      hasQuotation: false,
      totalAmount: '0.00',
      finalAmount: '0.00'
    }
  }

  // 处理所有报价记录
  const processedQuotations = quotationDataArray.map(quotation => {
    // 处理每个报价记录的检测项
    const processedItems = (quotation.items || []).map(item => {
      const price = parseFloat(item.price) || 0
      const pureGoldWeight = parseFloat(item.pureGoldWeight) || 0
      const itemTotal = price * pureGoldWeight // 价格 * 纯金重

      return {
        id: item.id,
        goldType: item.goldType,
        goldTypeText: getGoldTypeText(item.goldType),
        price: formatPrice(price),
        weight: formatWeight(item.weight),
        meltedWeight: formatWeight(item.meltedWeight),
        pureGoldWeight: formatWeight(pureGoldWeight),
        goldContent: item.goldContent ? `${item.goldContent}%` : '0%',
        imageUrl: item.imageUrl || '',
        itemTotal: formatPrice(itemTotal) // 单项总价 = 价格 * 纯金重
      }
    })

    // 计算当前报价记录的总价（所有检测项的 价格*纯金重 之和）
    const quotationTotal = processedItems.reduce((sum, item) => {
      return sum + (parseFloat(item.itemTotal) || 0)
    }, 0)

    return {
      id: quotation.id,
      orderId: quotation.orderId,
      createTime: quotation.createTime,
      updateTime: quotation.updateTime,
      createTimeFormatted: formatTime(quotation.createTime),
      updateTimeFormatted: formatTime(quotation.updateTime),
      items: processedItems,
      itemCount: processedItems.length,
      shippingFee: formatPrice(quotation.shippingFee),
      insuranceFee: formatPrice(quotation.insuranceFee),
      serviceFee: formatPrice(quotation.serviceFee),
      otherItems: formatPrice(quotation.otherItems),
      quotationTotal: formatPrice(quotationTotal),
      quotationFinalAmount: formatPrice(quotationTotal - (quotation.shippingFee || 0) - (quotation.insuranceFee || 0) - (quotation.serviceFee || 0) - (quotation.otherItems || 0))
    }
  })

  // 计算所有报价的汇总信息
  let totalAmount = 0
  let totalShippingFee = 0
  let totalInsuranceFee = 0
  let totalServiceFee = 0
  let totalOtherItems = 0

  processedQuotations.forEach(quotation => {
    totalAmount += parseFloat(quotation.quotationTotal) || 0
    totalShippingFee += parseFloat(quotation.shippingFee) || 0
    totalInsuranceFee += parseFloat(quotation.insuranceFee) || 0
    totalServiceFee += parseFloat(quotation.serviceFee) || 0
    totalOtherItems += parseFloat(quotation.otherItems) || 0
  })

  const totalFees = totalShippingFee + totalInsuranceFee + totalServiceFee + totalOtherItems
  const finalAmount = totalAmount - totalFees

  return {
    quotations: processedQuotations,
    quotationCount: processedQuotations.length,
    hasQuotation: processedQuotations.length > 0,
    totalAmount: formatPrice(totalAmount),
    totalShippingFee: formatPrice(totalShippingFee),
    totalInsuranceFee: formatPrice(totalInsuranceFee),
    totalServiceFee: formatPrice(totalServiceFee),
    totalOtherItems: formatPrice(totalOtherItems),
    totalFees: formatPrice(totalFees),
    finalAmount: formatPrice(finalAmount)
  }
}

// 格式化重量
function formatWeight(weight) {
  if (weight === null || weight === undefined) return '0.00'
  return parseFloat(weight).toFixed(2)
}

async function getExpressDetails(orderId) {
  addressLoading.value = true
  try {
    const response = await getExpressInfo(orderId)
    if (response.code === 200) {
      expressInfo.value = response.data
    } else {
      console.error(response.message || '获取快递信息失败')
    }
  } catch (error) {
    console.error('获取快递信息失败:', error)
  } finally {
    addressLoading.value = false
  }
}

// 填写快递信息 (从recycle.vue复制)
function handleFillExpressInfo(row) {
  expressForm.value = {
    orderId: row.orderId,
    expressCompany: '',
    trackingNumber: '',
    remark: ''
  }
  expressDialogVisible.value = true
}

// 确认快递信息并取件 (从recycle.vue复制)
async function confirmExpressInfo() {
  // 验证表单
  if (!expressForm.value.expressCompany || !expressForm.value.trackingNumber) {
    ElMessage.error('请填写完整的快递信息')
    return
  }

  expressSubmitLoading.value = true
  try {
    // 第一步：调用取件API - 使用新的参数格式
    await pickupOrder(
      expressForm.value.orderId,
      expressForm.value.expressCompany,
      expressForm.value.trackingNumber
    )

    // 第二步：更新订单状态为已取件(2)
    await updateRecycleOrderStatus(expressForm.value.orderId, 2)

    ElMessage.success('取件信息提交成功，订单状态已更新')
    expressDialogVisible.value = false
    getOrderDetails() // 刷新详情
  } catch (error) {
    console.error('取件操作失败:', error)
    ElMessage.error(`取件失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    expressSubmitLoading.value = false
  }
}

// 已签收操作 - 状态从2变为3 (从recycle.vue复制)
async function handleSignReceived(row) {
  try {
    await ElMessageBox.confirm('确定该订单已签收吗？', '确认签收', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateRecycleOrderStatus(row.orderId, 3)
    ElMessage.success('签收确认成功')
    getOrderDetails() // 刷新详情
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 检测操作 - 状态从3变为4 (从recycle.vue复制)
function handleInspection(row) {
  inspectionForm.value = {
    orderId: row.orderId,
    items: [
      {
        goldType: '',
        price: null,
        weight: null,
        meltedWeight: null,
        pureGoldWeight: null,
        goldContent: null,
        imageUrl: ''
      }
    ],
    shippingFee: 0,
    insuranceFee: 0,
    serviceFee: 0,
    otherItems: ''
  }
  inspectionDialogVisible.value = true
}

// 添加检测结果项
function addInspectionItem() {
  inspectionForm.value.items.push({
    goldType: '',
    price: null,
    weight: null,
    meltedWeight: null,
    pureGoldWeight: null,
    goldContent: null,
    imageUrl: ''
  })
}

// 删除检测结果项
function removeInspectionItem(index) {
  if (inspectionForm.value.items.length > 1) {
    inspectionForm.value.items.splice(index, 1)
  }
}

// 图片上传成功处理
function handleImageSuccess(response, index) {
  console.log('图片上传成功:', response)
  if (response.code === 200 && response.data) {
    // 拼接完整的图片地址
    const fullImageUrl = `https://www.iejhsgold.cn${response.data}`
    inspectionForm.value.items[index].imageUrl = fullImageUrl
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 图片上传失败处理
function handleImageError(error) {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

// 图片上传前验证
function beforeImageUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 确认检测结果 (从recycle.vue复制)
async function confirmInspection() {
  // 验证表单
  const hasValidItems = inspectionForm.value.items.some(item =>
    item.goldType && item.price !== null && item.weight !== null &&
    item.meltedWeight !== null && item.pureGoldWeight !== null &&
    item.goldContent !== null
  )

  if (!hasValidItems) {
    ElMessage.error('请至少填写一个完整的检测结果项')
    return
  }

  inspectionSubmitLoading.value = true
  try {
    // 构建报价数据
    const quotationData = {
      orderId: inspectionForm.value.orderId,
      items: inspectionForm.value.items.filter(item =>
        item.goldType && item.price !== null && item.weight !== null &&
        item.meltedWeight !== null && item.pureGoldWeight !== null &&
        item.goldContent !== null
      ),
      shippingFee: inspectionForm.value.shippingFee || 0,
      insuranceFee: inspectionForm.value.insuranceFee || 0,
      serviceFee: inspectionForm.value.serviceFee || 0,
      otherItems: inspectionForm.value.otherItems || ''
    }

    console.log('提交报价数据:', quotationData)

    // 第一步：创建报价
    await createQuotation(quotationData)

    // 第二步：更新订单状态为已检测(4)
    await updateRecycleOrderStatus(inspectionForm.value.orderId, 4)

    ElMessage.success('检测结果提交成功')
    inspectionDialogVisible.value = false
    getOrderDetails() // 刷新详情
  } catch (error) {
    console.error('检测结果提交失败:', error)
    ElMessage.error(`提交失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    inspectionSubmitLoading.value = false
  }
}

// 获取订单的纯金重总和
async function getTotalPureGoldWeight(orderId) {
  try {
    const response = await getQuotationByOrderId(orderId)
    if (response.data.code === 200 && response.data.data) {
      const quotations = response.data.data
      let totalPureGoldWeight = 0

      // 遍历所有报价记录
      quotations.forEach(quotation => {
        if (quotation.items && Array.isArray(quotation.items)) {
          // 遍历每个报价的检测项
          quotation.items.forEach(item => {
            if (item.pureGoldWeight) {
              totalPureGoldWeight += parseFloat(item.pureGoldWeight) || 0
            }
          })
        }
      })

      console.log(`订单 ${orderId} 的纯金重总和: ${totalPureGoldWeight}g`)
      return totalPureGoldWeight
    }
  } catch (error) {
    console.error('获取订单检测结果失败:', error)
  }
  return 0
}

// 完成订单 - 状态从5变为6 (从recycle.vue复制)
async function handleComplete(row) {
  try {
    await ElMessageBox.confirm('确定要完成该回收订单吗？', '完成订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 更新订单状态
    await updateOrderStatus(row.orderId, 6)

    // 处理返点分佣
    if (row.account) {
      // 获取订单的纯金重总和
      const totalPureGoldWeight = await getTotalPureGoldWeight(row.orderId)

      if (totalPureGoldWeight > 0) {
        console.log('开始处理返点分佣:', { account: row.account, weight: totalPureGoldWeight })
        await processRebateWithLoading(row.account, totalPureGoldWeight, (loading) => {
          // 这里可以设置加载状态，但由于是在完成订单后处理，暂时不显示额外的loading
          console.log('返点处理loading状态:', loading)
        })
      } else {
        console.warn('订单纯金重为0，跳过返点处理:', { account: row.account, orderId: row.orderId })
      }
    } else {
      console.warn('订单信息不完整，跳过返点处理:', { account: row.account, orderId: row.orderId })
    }

    ElMessage.success('回收订单已完成')
    getOrderDetails() // 刷新详情
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 已送到店功能 (从recycle.vue复制)
function handleDeliveredToStore(row) {
  deliveredForm.value = {
    orderId: row.orderId,
    finalPrice: row.estimatedPrice || null,
    totalWeight: row.estimatedWeight || null
  }
  deliveredDialogVisible.value = true
}

async function confirmDelivered() {
  // 验证表单
  if (!deliveredForm.value.finalPrice || deliveredForm.value.finalPrice <= 0) {
    ElMessage.error('请输入有效的最终价格')
    return
  }

  if (!deliveredForm.value.totalWeight || deliveredForm.value.totalWeight <= 0) {
    ElMessage.error('请输入有效的总克重')
    return
  }

  deliveredLoading.value = true
  try {
    // 第一步：设置最终价格
    await setFinalPrice(deliveredForm.value.orderId, deliveredForm.value.finalPrice)

    // 第二步：更新订单状态为已完成(6)
    await updateOrderStatus(deliveredForm.value.orderId, 6)

    // 第三步：处理返点分佣
    // 使用表单中输入的总克重进行返点计算
    if (orderDetail.value && orderDetail.value.account && deliveredForm.value.totalWeight) {
      console.log('开始处理返点分佣:', { account: orderDetail.value.account, weight: deliveredForm.value.totalWeight })
      await processRebateWithLoading(orderDetail.value.account, deliveredForm.value.totalWeight, (loading) => {
        // 返点处理的loading状态已经包含在deliveredLoading中
        console.log('返点处理loading状态:', loading)
      })
    } else {
      console.warn('订单信息不完整，跳过返点处理:', {
        orderId: deliveredForm.value.orderId,
        account: orderDetail.value?.account,
        weight: deliveredForm.value.totalWeight
      })
    }

    ElMessage.success('操作成功，订单已完成')
    deliveredDialogVisible.value = false
    getOrderDetails() // 刷新详情
  } catch (error) {
    console.error('已送到店操作失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    deliveredLoading.value = false
  }
}

function getStatusType(status) {
  const statusMap = {
    0: 'danger',     // 已取消
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  if (!time) return ''
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

function formatPrice(price) {
  return price ? price.toFixed(2) : '0.00'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function getExpressCompanyName(company) {
  const expressMap = {
    'SF': '顺丰速运',
    'ZTO': '中通快递',
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YD': '韵达速递',
    'BEST': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递',
    'EMS': '邮政EMS',
    'HHTT': '天天快递'
  }
  return expressMap[company] || company
}

function getReceiverName() {
  // 优先使用地址详情中的收货人信息
  if (addressInfo.value?.receiverName) {
    return addressInfo.value.receiverName
  }
  // 备用：使用订单中的收货人信息
  return orderDetail.value?.receiverName || ''
}

function getReceiverPhone() {
  // 优先使用地址详情中的电话信息
  if (addressInfo.value?.receiverPhone) {
    return addressInfo.value.receiverPhone
  }
  // 备用：使用订单中的电话信息
  return orderDetail.value?.receiverPhone || ''
}

function getFullAddress() {
  // 优先使用地址详情中的完整地址信息
  if (addressInfo.value) {
    const { province, city, district, detailAddress } = addressInfo.value
    return `${province || ''}${city || ''}${district || ''}${detailAddress || ''}`.trim()
  }
  // 备用：使用订单中的地址信息
  return orderDetail.value?.addressInfo || ''
}

onMounted(() => {
  getOrderDetails()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-detail {
  font-size: 14px;
}

.text-muted {
  color: #999;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons {
  text-align: center;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 检测弹窗样式 */
.inspection-items {
  max-height: 600px;
  overflow-y: auto;
}

.inspection-item {
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.image-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s ease;
}

.image-uploader:hover {
  border-color: #409eff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
}

.image-uploader-icon {
  font-size: 32px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
  line-height: 1.2;
}

.uploaded-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
  border-radius: 6px;
}

.upload-tips {
  flex: 1;
  padding-left: 16px;
}

.upload-tips p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

.unit {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 检测结果显示样式 */
.quotation-display {
  margin-top: 20px;
}

.quotation-record {
  margin-bottom: 24px;
}

.quotation-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.quotation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quotation-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.quotation-time {
  font-size: 14px;
  color: #909399;
}

.quotation-items-display {
  margin-bottom: 20px;
}

.quotation-item-display {
  margin-bottom: 16px;
}

.item-display-card {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.item-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.item-content-display {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.item-image-display {
  flex-shrink: 0;
  width: 150px;
}

.no-image-display {
  width: 150px;
  height: 150px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 14px;
}

.no-image-display .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.item-data-display {
  flex: 1;
}

.quotation-fee-display {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.quotation-fee-display h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.total-fee-display {
  margin-top: 24px;
}

.total-fee-card {
  border: 2px solid #1890ff;
  border-radius: 8px;
  background-color: #f0f8ff;
}
</style>
