// pages/product/detail.js
const app = getApp();

Page({
  data: {
    productId: '',
    product: null,
    currentSwiper: 0,
    isFavorite: false,
    quantity: 1,
    showShareModal: false,
    shareImage: '', // 分享图片
    shareCode: '', // 分享码
    goldPrice: {
      sellPrice: 0
    }
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        productId: options.id
      });
      this.loadProductDetail();
      this.checkFavorite();
      this.loadGoldPrice();
    }

    // 检查是否有邀请参数
    if (options.inviter) {
      app.globalData.inviter = options.inviter;
    }

    // 生成分享码
    this.generateShareCode();
  },

  onShareAppMessage: function () {
    return {
      title: this.data.product ? this.data.product.name : '精美黄金饰品',
      path: '/pages/product/detail?id=' + this.data.productId + '&inviter=' + (app.globalData.openid || ''),
      imageUrl: this.data.product ? this.data.product.images[0] : '/images/share-cover.jpg'
    };
  },

  // 加载商品详情
  loadProductDetail: function () {
    const db = wx.cloud.database();

    db.collection('products')
      .doc(this.data.productId)
      .get()
      .then(res => {
        this.setData({
          product: res.data
        });
      })
      .catch(err => {
        console.error('获取商品详情失败', err);
        wx.showToast({
          title: '获取商品详情失败',
          icon: 'none'
        });
      });
  },

  // 检查是否已收藏
  checkFavorite: function () {
    if (!app.globalData.openid) {
      return;
    }

    const db = wx.cloud.database();

    db.collection('favorites')
      .where({
        userId: app.globalData.openid,
        type: 'product',
        itemId: this.data.productId
      })
      .get()
      .then(res => {
        this.setData({
          isFavorite: res.data.length > 0
        });
      });
  },

  // 加载金价
  loadGoldPrice: function () {
    const db = wx.cloud.database();

    db.collection('goldPrices')
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()
      .then(res => {
        if (res.data && res.data.length > 0) {
          this.setData({
            'goldPrice.sellPrice': res.data[0].sellPrice
          });
        }
      });
  },

  // 轮播图切换
  swiperChange: function (e) {
    this.setData({
      currentSwiper: e.detail.current
    });
  },

  // 收藏/取消收藏
  toggleFavorite: function () {
    if (!app.globalData.openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const db = wx.cloud.database();

    if (this.data.isFavorite) {
      // 取消收藏
      db.collection('favorites')
        .where({
          userId: app.globalData.openid,
          type: 'product',
          itemId: this.data.productId
        })
        .get()
        .then(res => {
          if (res.data.length > 0) {
            return db.collection('favorites').doc(res.data[0]._id).remove();
          }
        })
        .then(() => {
          this.setData({
            isFavorite: false
          });
          wx.showToast({
            title: '已取消收藏',
            icon: 'success'
          });
        });
    } else {
      // 添加收藏
      db.collection('favorites')
        .add({
          data: {
            userId: app.globalData.openid,
            type: 'product',
            itemId: this.data.productId,
            createTime: db.serverDate()
          }
        })
        .then(() => {
          this.setData({
            isFavorite: true
          });
          wx.showToast({
            title: '收藏成功',
            icon: 'success'
          });
        });
    }
  },

  // 减少数量
  minusQuantity: function () {
    if (this.data.quantity <= 1) {
      return;
    }
    this.setData({
      quantity: this.data.quantity - 1
    });
  },

  // 增加数量
  addQuantity: function () {
    if (this.data.product && this.data.quantity >= this.data.product.stock) {
      return;
    }
    this.setData({
      quantity: this.data.quantity + 1
    });
  },

  // 输入数量
  inputQuantity: function (e) {
    let value = parseInt(e.detail.value);
    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (this.data.product && value > this.data.product.stock) {
      value = this.data.product.stock;
    }
    this.setData({
      quantity: value
    });
  },

  // 立即购买
  buyNow: function () {
    if (!app.globalData.openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    if (!this.data.product) {
      return;
    }

    // 创建订单
    const db = wx.cloud.database();

    const orderData = {
      userId: app.globalData.openid,
      products: [
        {
          productId: this.data.product._id,
          name: this.data.product.name,
          image: this.data.product.images[0],
          price: this.data.product.price,
          count: this.data.quantity,
          totalPrice: this.data.product.price * this.data.quantity
        }
      ],
      totalAmount: this.data.product.price * this.data.quantity,
      status: 'pending',
      address: {},
      inviterIds: app.globalData.inviter ? [app.globalData.inviter] : [],
      commissions: [],
      createTime: db.serverDate()
    };

    // 计算佣金
    if (app.globalData.inviter) {
      // 一级分享者佣金（假设为商品价格的5%）
      const commission1 = Math.floor(orderData.totalAmount * 0.05);

      orderData.commissions.push({
        userId: app.globalData.inviter,
        amount: commission1,
        level: 0,
        status: 'pending'
      });

      // 查询一级分享者的邀请人（二级分享）
      db.collection('users')
        .doc(app.globalData.inviter)
        .get()
        .then(res => {
          if (res.data && res.data.inviter) {
            // 二级分享者佣金（假设为商品价格的2%）
            const commission2 = Math.floor(orderData.totalAmount * 0.02);

            orderData.commissions.push({
              userId: res.data.inviter,
              amount: commission2,
              level: 1,
              status: 'pending'
            });

            orderData.inviterIds.push(res.data.inviter);
          }

          // 创建订单
          return db.collection('orders').add({
            data: orderData
          });
        })
        .then(res => {
          // 跳转到订单详情页
          wx.navigateTo({
            url: '/pages/order/detail?id=' + res._id
          });
        });
    } else {
      // 没有邀请人，直接创建订单
      db.collection('orders')
        .add({
          data: orderData
        })
        .then(res => {
          // 跳转到订单详情页
          wx.navigateTo({
            url: '/pages/order/detail?id=' + res._id
          });
        });
    }
  },

  // 显示分享面板
  showShare: function () {
    this.setData({
      showShareModal: true
    });
  },

  // 隐藏分享面板
  hideShare: function () {
    this.setData({
      showShareModal: false
    });
  },

  // 生成分享码
  generateShareCode: function () {
    // 在实际项目中，这里应该调用云函数生成带参数的小程序码
    // 这里简化处理，使用一个固定的分享图片
    this.setData({
      shareImage: '/images/share-qrcode.jpg',
      shareCode: app.globalData.openid || 'default'
    });
  },

  // 保存分享图片到相册
  saveShareImage: function () {
    wx.showLoading({
      title: '保存中...',
    });

    // 获取用户授权
    wx.getSetting({
      success: res => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.saveImage();
            },
            fail: () => {
              wx.hideLoading();
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false
              });
            }
          });
        } else {
          this.saveImage();
        }
      }
    });
  },

  // 保存图片
  saveImage: function () {
    wx.getImageInfo({
      src: this.data.shareImage,
      success: res => {
        wx.saveImageToPhotosAlbum({
          filePath: res.path,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: () => {
            wx.hideLoading();
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '获取图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage: function (e) {
    const current = e.currentTarget.dataset.src;
    const urls = this.data.product.images;

    wx.previewImage({
      current: current,
      urls: urls
    });
  }
})
