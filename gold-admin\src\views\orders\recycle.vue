<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入订单号或用户账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="已取消" :value="0" />
        <el-option label="已下单" :value="1" />
        <el-option label="已取件" :value="2" />
        <el-option label="待检测" :value="3" />
        <el-option label="已检测" :value="4" />
        <el-option label="已确认" :value="5" />
        <el-option label="订单已完成" :value="6" />
      </el-select>
      <el-select
        v-model="listQuery.goldType"
        placeholder="贵金属类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金" value="jewelry" />
        <el-option label="铂金" value="bar" />
        <el-option label="钯金" value="broken" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-input-number
        v-model="listQuery.estimatedPrice"
        placeholder="预估价格"
        :precision="2"
        :min="0"
        style="width: 150px"
        class="filter-item"
        controls-position="right"
        @keyup.enter="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        icon="Refresh"
        @click="handleReset"
      >
        重置
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="Download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="订单号" width="300">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">
            {{ scope.row.orderId }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="auto">
        <template #default="scope">
          {{ scope.row.account }}
        </template>
      </el-table-column>

      <el-table-column label="贵金属类型" width="100">
        <template #default="scope">
          {{ getGoldTypeText(scope.row.goldType) }}
        </template>
      </el-table-column>

      <el-table-column label="成色" width="80">
        <template #default="scope">
          {{ scope.row.purity }}
        </template>
      </el-table-column>

      <el-table-column label="预估重量(g)" width="120" align="right">
        <template #default="scope">
          {{ scope.row.estimatedWeight }}
        </template>
      </el-table-column>

      <el-table-column label="预估价格" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.estimatedPrice }}
        </template>
      </el-table-column>

      <el-table-column label="最终价格" width="120" align="right">
        <template #default="scope">
          <span v-if="scope.row.finalPrice">¥{{ scope.row.finalPrice }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="350" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="success"
            @click="handleFillExpressInfo(scope.row)"
          >
            填写快递单号
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="small"
            type="warning"
            @click="handleSignReceived(scope.row)"
          >
            已签收
          </el-button>
          <el-button
            v-if="scope.row.status === 3"
            size="small"
            type="primary"
            @click="handleInspection(scope.row)"
          >
            检测
          </el-button>
          <el-button
            v-if="scope.row.status === 5"
            size="small"
            type="success"
            @click="handleComplete(scope.row)"
          >
            完成订单
          </el-button>
          <!-- 已送到店按钮 - 只在状态1(已下单)时显示 -->
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="success"
            @click="handleDeliveredToStore(scope.row)"
          >
            已送到店
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />



    <!-- 已送到店对话框 -->
    <el-dialog
      title="设置最终价格"
      v-model="deliveredDialogVisible"
      width="400px"
    >
      <el-form
        ref="deliveredFormRef"
        :model="deliveredForm"
        :rules="deliveredRules"
        label-width="100px"
      >
        <el-form-item label="订单号">
          <el-input v-model="deliveredForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="最终价格" prop="finalPrice">
          <el-input-number
            v-model="deliveredForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入最终价格"
          />
        </el-form-item>
        <el-form-item label="总克重" prop="totalWeight">
          <el-input-number
            v-model="deliveredForm.totalWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入总克重"
          />
          <span style="margin-left: 8px; color: #999;">克</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deliveredDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmDelivered" :loading="deliveredLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写快递信息对话框 -->
    <el-dialog
      title="填写快递信息"
      v-model="expressDialogVisible"
      width="500px"
    >
      <el-form
        ref="expressFormRef"
        :model="expressForm"
        :rules="expressRules"
        label-width="120px"
      >
        <el-form-item label="订单号">
          <el-input v-model="expressForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="快递公司" prop="expressCompany">
          <el-select
            v-model="expressForm.expressCompany"
            placeholder="请选择快递公司"
            style="width: 100%"
          >
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="申通快递" value="STO" />
            <el-option label="韵达速递" value="YD" />
            <el-option label="百世快递" value="BEST" />
            <el-option label="德邦快递" value="DBL" />
            <el-option label="京东快递" value="JD" />
            <el-option label="邮政EMS" value="EMS" />
            <el-option label="天天快递" value="HHTT" />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="expressForm.trackingNumber"
            placeholder="请输入快递单号"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="expressForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="expressDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmExpressInfo" :loading="expressSubmitLoading">
            确认取件
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 检测结果录入对话框 -->
    <el-dialog
      title="录入检测结果"
      v-model="inspectionDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="inspectionFormRef"
        :model="inspectionForm"
        :rules="inspectionRules"
        label-width="120px"
      >
        <el-form-item label="订单号">
          <el-input v-model="inspectionForm.orderId" readonly />
        </el-form-item>

        <!-- 检测结果项列表 -->
        <el-form-item label="检测结果项">
          <div class="inspection-items">
            <div
              v-for="(item, index) in inspectionForm.items"
              :key="index"
              class="inspection-item"
            >
              <el-card class="item-card">
                <template #header>
                  <div class="card-header">
                    <span>检测项 {{ index + 1 }}</span>
                    <el-button
                      v-if="inspectionForm.items.length > 1"
                      type="danger"
                      size="small"
                      @click="removeInspectionItem(index)"
                      :icon="Delete"
                    >
                      删除
                    </el-button>
                  </div>
                </template>

                <!-- 第一行：基本信息 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.goldType`"
                      :rules="[{ required: true, message: '请输入黄金类型', trigger: 'blur' }]"
                      label="黄金类型"
                      label-width="100px"
                    >
                      <el-input
                        v-model="item.goldType"
                        placeholder="请输入黄金类型，如：Au9999、Au999、足金等"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.price`"
                      :rules="[{ required: true, message: '请输入价格', trigger: 'blur' }]"
                      label="价格"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.price"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入价格"
                      />
                      <span class="unit">元</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.goldContent`"
                      :rules="[{ required: true, message: '请输入含金量', trigger: 'blur' }]"
                      label="含金量"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.goldContent"
                        :precision="2"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                        placeholder="请输入含金量"
                      />
                      <span class="unit">%</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行：重量信息 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.weight`"
                      :rules="[{ required: true, message: '请输入克重', trigger: 'blur' }]"
                      label="克重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.weight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入克重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.meltedWeight`"
                      :rules="[{ required: true, message: '请输入熔后重', trigger: 'blur' }]"
                      label="熔后重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.meltedWeight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入熔后重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :prop="`items.${index}.pureGoldWeight`"
                      :rules="[{ required: true, message: '请输入纯金重', trigger: 'blur' }]"
                      label="纯金重"
                      label-width="100px"
                    >
                      <el-input-number
                        v-model="item.pureGoldWeight"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        placeholder="请输入纯金重"
                      />
                      <span class="unit">g</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第三行：检测图片 -->
                <el-row :gutter="20" class="form-row">
                  <el-col :span="24">
                    <el-form-item
                      label="检测图片"
                      label-width="100px"
                    >
                      <div class="image-upload-container">
                        <el-upload
                          class="image-uploader"
                          :action="uploadAction"
                          :headers="uploadHeaders"
                          :show-file-list="false"
                          :on-success="(response) => handleImageSuccess(response, index)"
                          :on-error="handleImageError"
                          :before-upload="beforeImageUpload"
                          accept="image/*"
                        >
                          <img v-if="item.imageUrl" :src="item.imageUrl" class="uploaded-image" />
                          <div v-else class="upload-placeholder">
                            <el-icon class="image-uploader-icon"><Plus /></el-icon>
                            <div class="upload-text">点击上传检测图片</div>
                          </div>
                        </el-upload>
                        <div class="upload-tips">
                          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>

            <el-button
              type="primary"
              @click="addInspectionItem"
              :icon="Plus"
              style="width: 100%; margin-top: 10px;"
            >
              添加检测结果项
            </el-button>
          </div>
        </el-form-item>

        <!-- 费用信息 -->
        <el-divider content-position="left">费用信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="总运费" prop="shippingFee">
              <el-input-number
                v-model="inspectionForm.shippingFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入总运费"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保费" prop="insuranceFee">
              <el-input-number
                v-model="inspectionForm.insuranceFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入保费"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务费" prop="serviceFee">
              <el-input-number
                v-model="inspectionForm.serviceFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入服务费"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 其他项目信息 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="其他项目" prop="otherItems">
              <el-input-number
                v-model="inspectionForm.otherItems"
                type="textarea"
                :rows="3"
                placeholder="请输入其他项目信息（可选）"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inspectionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInspection" :loading="inspectionSubmitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import { getOrderList, updateOrderStatus, setFinalPrice, pickupOrder, exportOrders, updateRecycleOrderStatus, updateInspectionResult, setRecycleFinalPrice, uploadFile, createQuotation, getQuotationByOrderId } from '@/api/orders'
import { parseTime } from '@/utils'
import { useUserStore } from '@/stores/user'
import Pagination from '@/components/Pagination/index.vue'
import { processRebateWithLoading } from '@/utils/rebate'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  pageNum: 1,
  pageSize: 20,
  keyword: '',
  status: '',
  goldType: '',
  estimatedPrice: null
})

// 移除不再使用的旧对话框相关变量

// 已送到店对话框相关
const deliveredDialogVisible = ref(false)
const deliveredLoading = ref(false)
const deliveredForm = ref({
  orderId: '',
  finalPrice: null,
  totalWeight: null
})
const deliveredRules = {
  finalPrice: [
    { required: true, message: '请输入最终价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最终价格必须大于等于0', trigger: 'blur' }
  ],
  totalWeight: [
    { required: true, message: '请输入总克重', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总克重必须大于0', trigger: 'blur' }
  ]
}

// 快递信息对话框相关
const expressDialogVisible = ref(false)
const expressSubmitLoading = ref(false)
const expressForm = ref({
  orderId: '',
  expressCompany: '',
  trackingNumber: '',
  remark: ''
})
const expressRules = {
  expressCompany: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: '请输入快递单号', trigger: 'blur' },
    { min: 8, max: 30, message: '快递单号长度应在8-30个字符之间', trigger: 'blur' }
  ]
}

// 检测结果对话框相关
const inspectionDialogVisible = ref(false)
const inspectionSubmitLoading = ref(false)
const userStore = useUserStore()

// 上传相关配置
const uploadAction = computed(() => {
  return import.meta.env.DEV ? '/api/minio/upload' : 'https://www.iejhsgold.cn/api/minio/upload'
})
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

const inspectionForm = ref({
  orderId: '',
  items: [
    {
      goldType: '',
      price: null,
      weight: null,
      meltedWeight: null,
      pureGoldWeight: null,
      goldContent: null,
      imageUrl: ''
    }
  ],
  shippingFee: 0,
  insuranceFee: 0,
  serviceFee: 0,
  otherItems: ''
})

const inspectionRules = {
  shippingFee: [
    { type: 'number', min: 0, message: '总运费必须大于等于0', trigger: 'blur' }
  ],
  insuranceFee: [
    { type: 'number', min: 0, message: '保费必须大于等于0', trigger: 'blur' }
  ],
  serviceFee: [
    { type: 'number', min: 0, message: '服务费必须大于等于0', trigger: 'blur' }
  ]
}

async function getList() {
  listLoading.value = true
  try {
    // 构建请求参数，过滤空值
    const params = {}
    Object.keys(listQuery.value).forEach(key => {
      const value = listQuery.value[key]
      if (value !== null && value !== undefined && value !== '') {
        params[key] = value
      }
    })

    const response = await getOrderList(params)

    // 适配后端返回的IPage结构
    if (response.data) {
      // 处理分页数据结构
      if (response.data.records) {
        // IPage结构：{records: [], total: number, size: number, current: number}
        list.value = response.data.records || []
        total.value = response.data.total || 0
      } else if (response.data.list) {
        // 兼容其他可能的结构
        list.value = response.data.list || []
        total.value = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        // 直接返回数组的情况
        list.value = response.data
        total.value = response.data.length
      } else {
        list.value = []
        total.value = 0
      }
    } else {
      list.value = []
      total.value = 0
    }

    console.log('获取回收订单列表成功:', {
      total: total.value,
      count: list.value.length,
      params,
      responseStructure: response.data ? Object.keys(response.data) : 'no data'
    })
  } catch (error) {
    console.error('获取回收订单列表失败:', error)
    console.error('请求参数:', params)
    console.error('错误详情:', error.response?.data || error.message)
    ElMessage.error(`获取订单列表失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      list.value = [
        {
          id: 1,
          orderId: 'REC20231201001',
          account: 'user001',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 10.5,
          estimatedPrice: 1500.00,
          finalPrice: null,
          status: 1, // 已下单
          description: '黄金戒指',
          inspectionResult: '',
          receiverName: '张三',
          receiverPhone: '***********',
          receiverAddress: '北京市朝阳区xxx',
          createTime: '2025-06-04T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 2,
          orderId: 'REC20231201002',
          account: 'user002',
          goldType: 'bar', // 铂金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 50.0,
          estimatedPrice: 7200.00,
          finalPrice: null,
          status: 2, // 已取件
          description: '铂金条',
          inspectionResult: '',
          receiverName: '李四',
          receiverPhone: '***********',
          receiverAddress: '上海市浦东新区xxx',
          createTime: '2025-06-03T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 3,
          orderId: 'REC20231201003',
          account: 'user003',
          goldType: 'broken', // 钯金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 31.1,
          estimatedPrice: 4500.00,
          finalPrice: null,
          status: 3, // 待检测
          description: '钯金饰品',
          inspectionResult: '',
          receiverName: '王五',
          receiverPhone: '***********',
          receiverAddress: '广州市天河区xxx',
          createTime: '2025-06-02T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 4,
          orderId: 'REC20231201004',
          account: 'user004',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 15.2,
          estimatedPrice: 2200.00,
          finalPrice: 2180.00,
          status: 4, // 已检测
          description: '黄金项链',
          inspectionResult: '成色符合标准，重量15.1g',
          receiverName: '赵六',
          receiverPhone: '***********',
          receiverAddress: '深圳市南山区xxx',
          createTime: '2025-06-01T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 5,
          orderId: 'REC20231201005',
          account: 'user005',
          goldType: 'bar', // 铂金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 100.0,
          estimatedPrice: 14400.00,
          finalPrice: 14350.00,
          status: 5, // 已确认
          description: '铂金条',
          inspectionResult: '成色符合标准，重量99.8g',
          receiverName: '孙七',
          receiverPhone: '***********',
          receiverAddress: '杭州市西湖区xxx',
          createTime: '2025-05-30T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 6,
          orderId: 'REC20231201006',
          account: 'user006',
          goldType: 'other', // 其他
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 8.5,
          estimatedPrice: 1200.00,
          finalPrice: 1180.00,
          status: 6, // 订单已完成
          description: '其他贵金属',
          inspectionResult: '成色符合标准，重量8.3g',
          receiverName: '周八',
          receiverPhone: '***********',
          receiverAddress: '成都市锦江区xxx',
          createTime: '2025-05-28T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 7,
          orderId: 'REC20231201007',
          account: 'user007',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 12.0,
          estimatedPrice: 1800.00,
          finalPrice: null,
          status: 0, // 已取消
          description: '黄金耳环',
          inspectionResult: '',
          receiverName: '李九',
          receiverPhone: '***********',
          receiverAddress: '武汉市洪山区xxx',
          createTime: '2025-05-26T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        }
      ]
      total.value = 7
    } else {
      list.value = []
      total.value = 0
    }
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.pageNum = 1
  getList()
}

function handleReset() {
  listQuery.value = {
    pageNum: 1,
    pageSize: 20,
    keyword: '',
    status: '',
    goldType: '',
    estimatedPrice: null
  }
  getList()
}

function handleDetail(row) {
  router.push(`/order-detail/${row.orderId}`)
}

// 填写快递信息
function handleFillExpressInfo(row) {
  expressForm.value = {
    orderId: row.orderId,
    expressCompany: '',
    trackingNumber: '',
    remark: ''
  }
  expressDialogVisible.value = true
}

// 确认快递信息并取件
async function confirmExpressInfo() {
  // 验证表单
  if (!expressForm.value.expressCompany || !expressForm.value.trackingNumber) {
    ElMessage.error('请填写完整的快递信息')
    return
  }

  expressSubmitLoading.value = true
  try {
    // 第一步：调用取件API - 使用新的参数格式
    await pickupOrder(
      expressForm.value.orderId,
      expressForm.value.expressCompany,
      expressForm.value.trackingNumber
    )

    // 第二步：更新订单状态为已取件(2)
    await updateRecycleOrderStatus(expressForm.value.orderId, 2)

    ElMessage.success('取件信息提交成功，订单状态已更新')
    expressDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('取件操作失败:', error)
    ElMessage.error(`取件失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    expressSubmitLoading.value = false
  }
}

// 已签收操作 - 状态从2变为3
async function handleSignReceived(row) {
  try {
    await ElMessageBox.confirm('确定该订单已签收吗？', '确认签收', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateRecycleOrderStatus(row.orderId, 3)
    ElMessage.success('签收确认成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 检测操作 - 状态从3变为4
function handleInspection(row) {
  inspectionForm.value = {
    orderId: row.orderId,
    items: [
      {
        goldType: '',
        price: null,
        weight: null,
        meltedWeight: null,
        pureGoldWeight: null,
        goldContent: null,
        imageUrl: ''
      }
    ],
    shippingFee: 0,
    insuranceFee: 0,
    serviceFee: 0,
    otherItems: ''
  }
  inspectionDialogVisible.value = true
}

// 添加检测结果项
function addInspectionItem() {
  inspectionForm.value.items.push({
    goldType: '',
    price: null,
    weight: null,
    meltedWeight: null,
    pureGoldWeight: null,
    goldContent: null,
    imageUrl: ''
  })
}

// 删除检测结果项
function removeInspectionItem(index) {
  if (inspectionForm.value.items.length > 1) {
    inspectionForm.value.items.splice(index, 1)
  }
}

// 图片上传成功处理
function handleImageSuccess(response, index) {
  console.log('图片上传成功:', response)
  if (response.code === 200 && response.data) {
    // 拼接完整的图片地址
    const fullImageUrl = `https://www.iejhsgold.cn${response.data}`
    inspectionForm.value.items[index].imageUrl = fullImageUrl
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 图片上传失败处理
function handleImageError(error) {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

// 图片上传前验证
function beforeImageUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 确认检测结果
async function confirmInspection() {
  // 验证表单
  const hasValidItems = inspectionForm.value.items.some(item =>
    item.goldType && item.price !== null && item.weight !== null &&
    item.meltedWeight !== null && item.pureGoldWeight !== null &&
    item.goldContent !== null
  )

  if (!hasValidItems) {
    ElMessage.error('请至少填写一个完整的检测结果项')
    return
  }

  inspectionSubmitLoading.value = true
  try {
    // 构建报价数据
    const quotationData = {
      orderId: inspectionForm.value.orderId,
      items: inspectionForm.value.items.filter(item =>
        item.goldType && item.price !== null && item.weight !== null &&
        item.meltedWeight !== null && item.pureGoldWeight !== null &&
        item.goldContent !== null
      ),
      shippingFee: inspectionForm.value.shippingFee || 0,
      insuranceFee: inspectionForm.value.insuranceFee || 0,
      serviceFee: inspectionForm.value.serviceFee || 0,
      otherItems: inspectionForm.value.otherItems || ''
    }

    console.log('提交报价数据:', quotationData)

    // 第一步：创建报价
    await createQuotation(quotationData)

    // 第二步：更新订单状态为已检测(4)
    await updateRecycleOrderStatus(inspectionForm.value.orderId, 4)

    ElMessage.success('检测结果提交成功')
    inspectionDialogVisible.value = false
    getList()
  } catch (error) {
    console.error('检测结果提交失败:', error)
    ElMessage.error(`提交失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    inspectionSubmitLoading.value = false
  }
}

// 获取订单的纯金重总和
async function getTotalPureGoldWeight(orderId) {
  try {
    const response = await getQuotationByOrderId(orderId)
    if (response.data.code === 200 && response.data.data) {
      const quotations = response.data.data
      let totalPureGoldWeight = 0

      // 遍历所有报价记录
      quotations.forEach(quotation => {
        if (quotation.items && Array.isArray(quotation.items)) {
          // 遍历每个报价的检测项
          quotation.items.forEach(item => {
            if (item.pureGoldWeight) {
              totalPureGoldWeight += parseFloat(item.pureGoldWeight) || 0
            }
          })
        }
      })

      console.log(`订单 ${orderId} 的纯金重总和: ${totalPureGoldWeight}g`)
      return totalPureGoldWeight
    }
  } catch (error) {
    console.error('获取订单检测结果失败:', error)
  }
  return 0
}

// 完成订单 - 状态从5变为6
async function handleComplete(row) {
  try {
    await ElMessageBox.confirm('确定要完成该回收订单吗？', '完成订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 更新订单状态
    await updateOrderStatus(row.orderId, 6)

    // 处理返点分佣
    if (row.account) {
      // 获取订单的纯金重总和
      const totalPureGoldWeight = await getTotalPureGoldWeight(row.orderId)

      if (totalPureGoldWeight > 0) {
        console.log('开始处理返点分佣:', { account: row.account, weight: totalPureGoldWeight })
        await processRebateWithLoading(row.account, totalPureGoldWeight, (loading) => {
          // 这里可以设置加载状态，但由于是在完成订单后处理，暂时不显示额外的loading
          console.log('返点处理loading状态:', loading)
        })
      } else {
        console.warn('订单纯金重为0，跳过返点处理:', { account: row.account, orderId: row.orderId })
      }
    } else {
      console.warn('订单信息不完整，跳过返点处理:', { account: row.account, orderId: row.orderId })
    }

    ElMessage.success('回收订单已完成')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportOrders(listQuery.value)
    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `回收订单数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 已送到店功能
function handleDeliveredToStore(row) {
  deliveredForm.value = {
    orderId: row.orderId,
    finalPrice: row.estimatedPrice || null,
    totalWeight: row.estimatedWeight || null
  }
  deliveredDialogVisible.value = true
}

async function confirmDelivered() {
  // 验证表单
  if (!deliveredForm.value.finalPrice || deliveredForm.value.finalPrice <= 0) {
    ElMessage.error('请输入有效的最终价格')
    return
  }

  if (!deliveredForm.value.totalWeight || deliveredForm.value.totalWeight <= 0) {
    ElMessage.error('请输入有效的总克重')
    return
  }

  deliveredLoading.value = true
  try {
    // 第一步：设置最终价格
    await setFinalPrice(deliveredForm.value.orderId, deliveredForm.value.finalPrice)

    // 第二步：更新订单状态为已完成(6)
    await updateOrderStatus(deliveredForm.value.orderId, 6)

    // 第三步：处理返点分佣
    // 使用表单中输入的总克重进行返点计算
    const currentOrder = list.value.find(item => item.orderId === deliveredForm.value.orderId)
    if (currentOrder && currentOrder.account && deliveredForm.value.totalWeight) {
      console.log('开始处理返点分佣:', { account: currentOrder.account, weight: deliveredForm.value.totalWeight })
      await processRebateWithLoading(currentOrder.account, deliveredForm.value.totalWeight, (loading) => {
        // 返点处理的loading状态已经包含在deliveredLoading中
        console.log('返点处理loading状态:', loading)
      })
    } else {
      console.warn('订单信息不完整，跳过返点处理:', {
        orderId: deliveredForm.value.orderId,
        account: currentOrder?.account,
        weight: deliveredForm.value.totalWeight
      })
    }

    ElMessage.success('操作成功，订单已完成')
    deliveredDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('已送到店操作失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    deliveredLoading.value = false
  }
}

function getStatusType(status) {
  const statusMap = {
    0: 'danger',     // 已取消
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function formatTime(time) {
  if (!time) return '-'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.text-muted {
  color: #999;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

/* 表格加载状态优化 */
.el-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 检测弹窗样式 */
.inspection-items {
  max-height: 600px;
  overflow-y: auto;
}

.inspection-item {
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.image-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s ease;
}

.image-uploader:hover {
  border-color: #409eff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
}

.image-uploader-icon {
  font-size: 32px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
  line-height: 1.2;
}

.uploaded-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
  border-radius: 6px;
}

.upload-tips {
  flex: 1;
  padding-left: 16px;
}

.upload-tips p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

.unit {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}
</style>
